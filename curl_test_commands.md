# cURL Test Commands for TaoPhieuThuTuHoaDon Filter

## Configuration
- **Base URL:** http://127.0.0.1:8000
- **Entity Slug:** thanhhuy-b75m33i5
- **Token:** 5ca361e477371412592007e446312d1afb9d3a6a
- **Filter Endpoint:** `/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/`

---

## 1. Test Endpoint Availability

```bash
# Test if endpoint exists (should return 405 Method Not Allowed)
curl -X GET \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

---

## 2. Validation Error Tests

### 2.1 Missing Required Fields
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 2.2 Invalid UUID Format
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "invalid-uuid-format"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 2.3 Non-existent UUID
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "12345678-1234-1234-1234-123456789012"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 2.4 Invalid xu_ly Value
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "3",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "12345678-1234-1234-1234-123456789012"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

---

## 3. Get Reference Data (to get real UUIDs)

### 3.1 Get ChungTu (Documents)
```bash
curl -X GET \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/documents/
```

### 3.2 Get Customers
```bash
curl -X GET \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/customers/
```

### 3.3 Get Currencies (Ngoai Te)
```bash
curl -X GET \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/ngoai-te/
```

### 3.4 Get Document Books (Quyen Chung Tu)
```bash
curl -X GET \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/quyen-chung-tu/
```

---

## 4. Filter Tests with Real Data

**Note:** Replace the UUIDs below with actual UUIDs from the reference data calls above.

### 4.1 Basic Filter - Create Mode (xu_ly = 1)
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "REPLACE_WITH_REAL_CHUNG_TU_UUID"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 4.2 Filter with Customer - Create Mode
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "REPLACE_WITH_REAL_CHUNG_TU_UUID",
    "ma_kh": "REPLACE_WITH_REAL_CUSTOMER_UUID"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 4.3 Filter with All Optional Fields - Create Mode
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "so_ct1": "HD001",
    "so_ct2": "HD999",
    "ma_ct": "REPLACE_WITH_REAL_CHUNG_TU_UUID",
    "ma_kh": "REPLACE_WITH_REAL_CUSTOMER_UUID",
    "ma_nt": "REPLACE_WITH_REAL_CURRENCY_UUID",
    "ma_nk": "REPLACE_WITH_REAL_DOCUMENT_BOOK_UUID",
    "user_id0": "admin"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 4.4 Basic Filter - Delete Mode (xu_ly = 2)
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "2",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "REPLACE_WITH_REAL_CHUNG_TU_UUID"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 4.5 Filter with Customer - Delete Mode
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "2",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "REPLACE_WITH_REAL_CHUNG_TU_UUID",
    "ma_kh": "REPLACE_WITH_REAL_CUSTOMER_UUID"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

---

## 5. Date Range Tests

### 5.1 Current Month Filter
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2025-06-01",
    "ngay_ct2": "2025-06-30",
    "ma_ct": "REPLACE_WITH_REAL_CHUNG_TU_UUID"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

### 5.2 Last 30 Days Filter
```bash
curl -X POST \
  -H "Authorization: Token 5ca361e477371412592007e446312d1afb9d3a6a" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2025-05-13",
    "ngay_ct2": "2025-06-12",
    "ma_ct": "REPLACE_WITH_REAL_CHUNG_TU_UUID"
  }' \
  http://127.0.0.1:8000/api/entities/thanhhuy-b75m33i5/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
```

---

## 6. Testing Workflow

1. **First, run the reference data calls** (section 3) to get real UUIDs
2. **Copy the UUIDs** from the responses
3. **Replace the placeholder UUIDs** in the filter test commands (section 4)
4. **Run the filter tests** to see actual data

### Expected Responses:

- **Validation Errors (400):** Should return clear error messages
- **Successful Filters (200):** Should return data based on xu_ly mode:
  - `xu_ly = "1"`: Returns hoa_don_ban_hang or hoa_don_dich_vu data
  - `xu_ly = "2"`: Returns phieu_thu data
- **Empty Results (200):** Valid request but no matching data

---

## 7. Quick Test Script

Save this as `test_filter.sh`:

```bash
#!/bin/bash

BASE_URL="http://127.0.0.1:8000"
ENTITY_SLUG="thanhhuy-b75m33i5"
TOKEN="5ca361e477371412592007e446312d1afb9d3a6a"
FILTER_URL="$BASE_URL/api/entities/$ENTITY_SLUG/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"

echo "Testing TaoPhieuThuTuHoaDon Filter API"
echo "===================================="

echo "1. Testing endpoint availability..."
curl -X GET -H "Authorization: Token $TOKEN" "$FILTER_URL"

echo -e "\n\n2. Testing missing required fields..."
curl -X POST \
  -H "Authorization: Token $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"xu_ly": "1"}' \
  "$FILTER_URL"

echo -e "\n\n3. Testing invalid UUID format..."
curl -X POST \
  -H "Authorization: Token $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "invalid-uuid"
  }' \
  "$FILTER_URL"

echo -e "\n\nTest completed!"
```

Make executable: `chmod +x test_filter.sh`
Run: `./test_filter.sh`
