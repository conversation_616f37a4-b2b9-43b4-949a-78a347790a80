#!/usr/bin/env python3
"""
Script to create HD1 (<PERSON><PERSON><PERSON> đơn bán hàng) and HD2 (<PERSON><PERSON>a đơn dịch vụ) ChungTu records
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ENTITY_SLUG = "thanhhuy-b75m33i5"
TOKEN = "5ca361e477371412592007e446312d1afb9d3a6a"

class ChungTuCreator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Token {TOKEN}',
            'Content-Type': 'application/json'
        })
        self.base_url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp"
        self.chung_tu_url = f"{self.base_url}/documents/"
        
    def create_chung_tu_hd1(self):
        """Create HD1 - H<PERSON>a đơn bán hàng"""
        print("🔧 Creating HD1 - <PERSON><PERSON>a đơn bán hàng...")
        
        hd1_data = {
            "ma_ct": "HD1",
            "ten_ct": "<PERSON>óa đơn bán hàng",
            "ten_ct2": "Sales Invoice",
            "ngay_ks": "2024-01-01",
            "stt": 1,
            "i_so_ct": 1000,
            "d_page_count": 1,
            "order_type": "0",
            "df_status": "5",
            "ct_kt_ton": "1",  # Kiểm tra tồn kho
            "loai_dl_ton": "1",
            "ct_sd_vi_tri": False,
            "ngay_lct_yn": True,
            "vc_link": "",
            "ct_save_log": "1",
            "xcode": "ban_hang",  # Loại bán hàng
            "user_id0_yn": True,
            "user_id2_yn": True
        }
        
        response = self.session.post(self.chung_tu_url, json=hd1_data)
        
        if response.status_code == 201:
            created_hd1 = response.json()
            print(f"✅ Created HD1 successfully!")
            print(f"   UUID: {created_hd1.get('uuid')}")
            print(f"   Mã CT: {created_hd1.get('ma_ct')}")
            print(f"   Tên CT: {created_hd1.get('ten_ct')}")
            return created_hd1
        elif response.status_code == 400:
            error_data = response.json()
            if 'ma_ct' in error_data and 'already exists' in str(error_data['ma_ct']):
                print("⚠️ HD1 already exists, trying to get existing record...")
                return self.get_existing_chung_tu("HD1")
            else:
                print(f"❌ Failed to create HD1: {error_data}")
                return None
        else:
            print(f"❌ Failed to create HD1: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    
    def create_chung_tu_hd2(self):
        """Create HD2 - Hóa đơn dịch vụ"""
        print("\n🔧 Creating HD2 - Hóa đơn dịch vụ...")
        
        hd2_data = {
            "ma_ct": "HD2",
            "ten_ct": "Hóa đơn dịch vụ",
            "ten_ct2": "Service Invoice",
            "ngay_ks": "2024-01-01",
            "stt": 2,
            "i_so_ct": 1000,
            "d_page_count": 1,
            "order_type": "0",
            "df_status": "5",
            "ct_kt_ton": "0",  # Không kiểm tra tồn kho cho dịch vụ
            "loai_dl_ton": "0",
            "ct_sd_vi_tri": False,
            "ngay_lct_yn": True,
            "vc_link": "",
            "ct_save_log": "1",
            "xcode": "ban_hang",  # Cũng thuộc loại bán hàng
            "user_id0_yn": True,
            "user_id2_yn": True
        }
        
        response = self.session.post(self.chung_tu_url, json=hd2_data)
        
        if response.status_code == 201:
            created_hd2 = response.json()
            print(f"✅ Created HD2 successfully!")
            print(f"   UUID: {created_hd2.get('uuid')}")
            print(f"   Mã CT: {created_hd2.get('ma_ct')}")
            print(f"   Tên CT: {created_hd2.get('ten_ct')}")
            return created_hd2
        elif response.status_code == 400:
            error_data = response.json()
            if 'ma_ct' in error_data and 'already exists' in str(error_data['ma_ct']):
                print("⚠️ HD2 already exists, trying to get existing record...")
                return self.get_existing_chung_tu("HD2")
            else:
                print(f"❌ Failed to create HD2: {error_data}")
                return None
        else:
            print(f"❌ Failed to create HD2: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    
    def get_existing_chung_tu(self, ma_ct):
        """Get existing ChungTu by ma_ct"""
        response = self.session.get(self.chung_tu_url)
        
        if response.status_code == 200:
            data = response.json()
            for chung_tu in data.get('results', []):
                if chung_tu.get('ma_ct') == ma_ct:
                    print(f"📋 Found existing {ma_ct}:")
                    print(f"   UUID: {chung_tu.get('uuid')}")
                    print(f"   Mã CT: {chung_tu.get('ma_ct')}")
                    print(f"   Tên CT: {chung_tu.get('ten_ct')}")
                    return chung_tu
        
        print(f"❌ Could not find existing {ma_ct}")
        return None
    
    def list_all_chung_tu(self):
        """List all existing ChungTu records"""
        print("\n📋 Listing all existing ChungTu records...")
        
        response = self.session.get(self.chung_tu_url)
        
        if response.status_code == 200:
            data = response.json()
            chung_tu_list = data.get('results', [])
            
            if chung_tu_list:
                print(f"Found {len(chung_tu_list)} ChungTu records:")
                print("-" * 80)
                print(f"{'Mã CT':<8} {'Tên CT':<30} {'UUID':<36} {'Loại':<15}")
                print("-" * 80)
                
                for ct in chung_tu_list:
                    ma_ct = ct.get('ma_ct', 'N/A')
                    ten_ct = ct.get('ten_ct', 'N/A')[:28]
                    uuid_str = ct.get('uuid', 'N/A')
                    xcode = ct.get('xcode', 'N/A')
                    print(f"{ma_ct:<8} {ten_ct:<30} {uuid_str:<36} {xcode:<15}")
                
                print("-" * 80)
            else:
                print("No ChungTu records found.")
        else:
            print(f"❌ Failed to list ChungTu: {response.status_code}")
            print(f"Response: {response.text}")
    
    def test_filter_with_created_chung_tu(self, hd1_data, hd2_data):
        """Test filter functionality with created ChungTu"""
        print("\n🧪 Testing filter with created ChungTu...")
        
        filter_url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
        if hd1_data:
            print(f"\n📝 Testing filter with HD1 (UUID: {hd1_data.get('uuid')})")
            
            test_data = {
                "xu_ly": "1",
                "ngay_ct1": "2024-01-01",
                "ngay_ct2": "2024-12-31",
                "ma_ct": hd1_data.get('uuid')
            }
            
            response = self.session.post(filter_url, json=test_data)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Filter test successful! Found {len(data.get('results', []))} results")
            else:
                print(f"❌ Filter test failed: {response.text}")
        
        if hd2_data:
            print(f"\n📝 Testing filter with HD2 (UUID: {hd2_data.get('uuid')})")
            
            test_data = {
                "xu_ly": "1",
                "ngay_ct1": "2024-01-01",
                "ngay_ct2": "2024-12-31",
                "ma_ct": hd2_data.get('uuid')
            }
            
            response = self.session.post(filter_url, json=test_data)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Filter test successful! Found {len(data.get('results', []))} results")
            else:
                print(f"❌ Filter test failed: {response.text}")
    
    def run(self):
        """Run the ChungTu creation process"""
        print("🚀 Creating HD1 and HD2 ChungTu Records")
        print("=" * 50)
        
        # List existing ChungTu first
        self.list_all_chung_tu()
        
        # Create HD1
        hd1_data = self.create_chung_tu_hd1()
        
        # Create HD2
        hd2_data = self.create_chung_tu_hd2()
        
        # List all ChungTu after creation
        self.list_all_chung_tu()
        
        # Test filter functionality
        self.test_filter_with_created_chung_tu(hd1_data, hd2_data)
        
        print("\n" + "=" * 50)
        if hd1_data and hd2_data:
            print("✅ Successfully created both HD1 and HD2!")
            print("\n📋 Summary:")
            print(f"HD1 UUID: {hd1_data.get('uuid')}")
            print(f"HD2 UUID: {hd2_data.get('uuid')}")
            print("\n🔧 You can now use these UUIDs in your filter tests!")
            
            # Generate cURL commands
            print("\n📝 cURL commands for testing:")
            print(f"""
# Test filter with HD1:
curl -X POST \\
  -H "Authorization: Token {TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "{hd1_data.get('uuid')}"
  }}' \\
  {BASE_URL}/api/entities/{ENTITY_SLUG}/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/

# Test filter with HD2:
curl -X POST \\
  -H "Authorization: Token {TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "xu_ly": "1",
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_ct": "{hd2_data.get('uuid')}"
  }}' \\
  {BASE_URL}/api/entities/{ENTITY_SLUG}/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/
            """)
        else:
            print("❌ Failed to create one or both ChungTu records")
            return False
        
        return True

if __name__ == "__main__":
    creator = ChungTuCreator()
    success = creator.run()
    
    if success:
        print("\n🎉 ChungTu creation completed successfully!")
    else:
        print("\n💥 ChungTu creation failed!")
        exit(1)
