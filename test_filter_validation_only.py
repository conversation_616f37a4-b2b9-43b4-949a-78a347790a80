#!/usr/bin/env python3
"""
Test script for TaoPhieuThuTuHoaDon filter validation only
Tests the updated serializer fields validation without requiring real data
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ENTITY_SLUG = "tutimi-dnus2xnc"
DIRECT_TOKEN = "d37d77e4655f5aff352da29d8b1953338193d389"

class FilterValidationTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Token {DIRECT_TOKEN}',
            'Content-Type': 'application/json'
        })
        self.filter_url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
    def test_validation_scenarios(self):
        """Test various validation scenarios"""
        print("🧪 Testing Filter Validation Scenarios")
        print("=" * 50)
        
        # Test Case 1: Missing required fields
        print("\n📝 Test Case 1: Missing required fields")
        
        invalid_data = {
            "xu_ly": "1"
            # Missing ngay_ct1, ngay_ct2, ma_ct
        }
        
        response = self.session.post(self.filter_url, json=invalid_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected validation error, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test Case 2: Invalid UUID format
        print("\n📝 Test Case 2: Invalid UUID format")
        
        invalid_uuid_data = {
            "xu_ly": "1",
            "ngay_ct1": "2024-01-01",
            "ngay_ct2": "2024-12-31",
            "ma_ct": "invalid-uuid-format"
        }
        
        response = self.session.post(self.filter_url, json=invalid_uuid_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ UUID validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected UUID validation error, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test Case 3: Valid UUID format but non-existent
        print("\n📝 Test Case 3: Valid UUID format but non-existent")
        
        nonexistent_uuid_data = {
            "xu_ly": "1",
            "ngay_ct1": "2024-01-01",
            "ngay_ct2": "2024-12-31",
            "ma_ct": "12345678-1234-1234-1234-123456789012"  # Valid format but doesn't exist
        }
        
        response = self.session.post(self.filter_url, json=nonexistent_uuid_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Non-existent UUID validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected validation error for non-existent UUID, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test Case 4: Invalid date range
        print("\n📝 Test Case 4: Invalid date range (start > end)")
        
        invalid_date_data = {
            "xu_ly": "1",
            "ngay_ct1": "2024-12-31",
            "ngay_ct2": "2024-01-01",  # End date before start date
            "ma_ct": "12345678-1234-1234-1234-123456789012"
        }
        
        response = self.session.post(self.filter_url, json=invalid_date_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Date range validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected date range validation error, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test Case 5: Valid format with all optional fields
        print("\n📝 Test Case 5: Valid format with all optional UUID fields")
        
        valid_format_data = {
            "xu_ly": "1",
            "ngay_ct1": "2024-01-01",
            "ngay_ct2": "2024-12-31",
            "ma_ct": "12345678-1234-1234-1234-123456789012",
            "ma_kh": "*************-4321-4321-************",
            "ma_nt": "11111111-**************-************",
            "ma_nk": "*************-8888-9999-000000000000",
            "unit_id": "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee",
            "user_id0": "test_user"
        }
        
        response = self.session.post(self.filter_url, json=valid_format_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Non-existent UUID validation errors correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        elif response.status_code == 200:
            print("✅ Request format is valid (though UUIDs don't exist)")
            data = response.json()
            print(f"Results count: {len(data.get('results', []))}")
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test Case 6: Test xu_ly field validation
        print("\n📝 Test Case 6: Invalid xu_ly value")
        
        invalid_xu_ly_data = {
            "xu_ly": "3",  # Invalid value (should be 1 or 2)
            "ngay_ct1": "2024-01-01",
            "ngay_ct2": "2024-12-31",
            "ma_ct": "12345678-1234-1234-1234-123456789012"
        }
        
        response = self.session.post(self.filter_url, json=invalid_xu_ly_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ xu_ly validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected xu_ly validation error, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        return True
    
    def test_endpoint_availability(self):
        """Test if the endpoint is available"""
        print("\n🔍 Testing endpoint availability...")
        
        # Simple GET request to see if endpoint exists
        response = self.session.get(self.filter_url)
        print(f"GET Status Code: {response.status_code}")
        
        if response.status_code == 405:
            print("✅ Endpoint exists (Method Not Allowed for GET is expected)")
        elif response.status_code == 404:
            print("❌ Endpoint not found")
        else:
            print(f"ℹ️ Unexpected status: {response.status_code}")
        
        return True
    
    def run_all_tests(self):
        """Run all validation tests"""
        print("🚀 Starting Filter Validation Tests")
        print("=" * 50)
        
        # Test endpoint availability
        if not self.test_endpoint_availability():
            return False
        
        # Test validation scenarios
        if not self.test_validation_scenarios():
            return False
        
        print("\n" + "=" * 50)
        print("✅ All validation tests completed!")
        return True

if __name__ == "__main__":
    tester = FilterValidationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Validation test suite passed!")
    else:
        print("\n💥 Validation test suite failed!")
        exit(1)
