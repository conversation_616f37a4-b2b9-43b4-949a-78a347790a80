# Tổng Hợp <PERSON>c Nghiệp Vụ ERP

## 📋 <PERSON>

1. [<PERSON><PERSON><PERSON> (Debt Management)](#công-nợ-debt-management)
2. [<PERSON><PERSON><PERSON> (Journal Entries)](#bút-toán-journal-entries)
3. [<PERSON><PERSON>i (Ledger)](#sổ-cái-ledger)
4. [<PERSON><PERSON> Chi Tiết Công Nợ](#sổ-chi-tiết-công-nợ)

---

## 🏦 <PERSON><PERSON>ng <PERSON> (Debt Management)

### Kh<PERSON>i <PERSON>m Cơ Bản

Khi thực hiện tạo các hóa đơn, phiếu thu, chi thì sẽ đều tạo **sổ cái**, **bút toán** và các **giao dịch** tương ứng. <PERSON><PERSON> theo chi tiết, thuế thì sẽ lưu tài kho<PERSON>n nợ, c<PERSON>, do<PERSON>h thu, thuế khác nhau.

### <PERSON><PERSON><PERSON>uan <PERSON> 1:1 <PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> và Sổ Cái

**🔗 <PERSON>uy tắc quan trọng:**
- **Mỗi h<PERSON><PERSON> đ<PERSON>, phi<PERSON><PERSON> chi, thu sẽ đều tạo đúng 1 sổ cái**
- **Mỗi hóa đơn, phiếu chỉ có đúng 1 sổ cái**
- **Sổ cái đó chỉ thuộc về đúng hóa đơn, phiếu đó**
- **Sổ cái có lưu 1 dạng ID để biết là sổ của hóa đơn, phiếu nào**

**📋 Ví dụ minh họa:**
```
Hóa đơn bán hàng HD001 → Tạo Sổ cái SC001 (chỉ cho HD001)
Phiếu thu PT002 → Tạo Sổ cái SC002 (chỉ cho PT002)
Phiếu chi PC003 → Tạo Sổ cái SC003 (chỉ cho PC003)
```

**🔍 Cách nhận biết:**
- Sổ cái sẽ có reference/link đến hóa đơn/phiếu gốc
- Có thể thông qua foreign key hoặc reference field
- Đảm bảo truy vết được từ sổ cái về hóa đơn/phiếu tạo ra nó

### 1. Bảng Lưu Trữ Tổng Công Nợ Hàng Năm

**Mục đích:** Tối ưu hóa việc tính toán công nợ cho năm hiện tại và tránh việc phải xử lý lại toàn bộ dữ liệu lịch sử.

**Cơ chế hoạt động:**
- **Ví dụ:** Để tính số dư nợ/có đầu kỳ của khách hàng A vào ngày 02/02/2025:
  - **Cách truyền thống:** Phải tính toán lại toàn bộ giao dịch từ khi bắt đầu có dữ liệu
  - **Cách tối ưu:**
    1. Lấy số dư cuối kỳ từ bảng tổng hợp tại 31/12/2024
    2. Chỉ tính các giao dịch từ 01/01/2025 đến 02/02/2025

### 2. Nguồn Dữ Liệu và Cách Tính Toán

Các số liệu công nợ được tổng hợp từ tất cả các bảng giao dịch có:
- **Khách hàng/Nhà cung cấp:** Đối tượng chính theo dõi công nợ
- **Tài khoản kế toán:** Trực tiếp hoặc suy ra từ model Khách hàng
- **Các trường tiền:** Giá trị tiền của giao dịch
- **Phân loại Thu/Chi:**
  - **Thu:** Giảm công nợ phải thu KH / Tăng công nợ phải trả NCC
  - **Chi:** Tăng công nợ phải thu KH / Giảm công nợ phải trả NCC

### 3. Cấu Trúc Bảng Cân Đối Phát Sinh Công Nợ

| Cột | Mô Tả | Cách Tính |
|-----|-------|-----------|
| **Dư Nợ/Có Đầu Kỳ** | Số dư tại thời điểm bắt đầu kỳ | Từ bảng tổng hợp hàng năm |
| **Phát Sinh Nợ/Có** | Giao dịch phát sinh trong kỳ | Tổng hợp từ các giao dịch trong khoảng thời gian |
| **Dư Nợ/Có Cuối Kỳ** | Số dư tại thời điểm kết thúc kỳ | `Đầu kỳ + PS Nợ - PS Có` |

### 4. Biểu Diễn Số Liệu Nợ/Có

**Quy ước lưu trữ:** Tất cả số liệu lưu trong **một cột số duy nhất**
- **Số dương (> 0):** Hiển thị ở cột **Nợ**
- **Số âm (< 0):** Hiển thị ở cột **Có** (lấy giá trị tuyệt đối)
- **Số không (= 0):** Không có dư nợ/có

---

## 📝 Bút Toán (Journal Entries)

### Khái Niệm Cơ Bản

Mỗi khi tạo bút toán sẽ tạo **2 giao dịch đối ứng**. Bút toán sẽ **gộp các tài khoản doanh thu hoặc thuế** vào chung 1 bút toán nếu có tài khoản giống nhau.

### Các Loại Bút Toán Theo Hóa Đơn

| Loại Hóa Đơn | Bút Toán Được Tạo | Điều Kiện |
|--------------|-------------------|-----------|
| **Hóa đơn mua hàng trong nước** | `CONGNO` + `THUE` | Luôn tạo |
| **Hóa đơn dịch vụ trả lại, giảm giá** | `DTCK` + `THUE` | Luôn tạo |
| **Hóa đơn bán hàng** | `DT0CK` + `THUE` | Luôn tạo |
| **Hóa đơn bán hàng (có chiết khấu)** | `DT0CK` + `THUE` + `GIAVON` | Khi chọn option chiết khấu |

**Lưu ý:** Nếu hóa đơn chọn **không kê khai thuế** ở chi tiết thì sẽ **không tạo bút toán THUE**.

### Mapping Tài Khoản Tự Động

**Hóa đơn bán hàng:**
- **Tài khoản Nợ:** Từ field `tk` của hóa đơn header
- **Tài khoản Có (Doanh thu):** Từ field `tk_dt` của chi tiết hóa đơn

**Ví dụ bút toán tự động:**
```
Header: tk = "131" (Phải thu khách hàng)
Chi tiết: tk_dt = "511" (Doanh thu bán hàng)
Số tiền: 1,000,000 VND

Bút toán tự động:
Nợ TK 131: 1,000,000 VND
Có TK 511: 1,000,000 VND
```

---

## 📚 Sổ Cái (Ledger)

### Khái Niệm

Sổ cái là nơi ghi nhận tất cả các bút toán kế toán của một đơn vị. Mỗi sổ cái thuộc về một entity và chứa nhiều bút toán.

### Đặc Điểm

- **Unique identifier:** Mỗi sổ cái có ID duy nhất
- **Tên sổ cái:** Có thể đặt tên mô tả (tối đa 150 ký tự)
- **Trạng thái:** Có thể là posted/unposted, locked/unlocked, hidden/visible
- **Thuộc về Entity:** Mỗi sổ cái thuộc về một đơn vị kế toán cụ thể

---

## 📊 Sổ Chi Tiết Công Nợ

### Cấu Trúc Dữ Liệu

- **Mỗi dòng data = 1 bút toán**
- **Nếu ấn chi tiết vật tư:** Hiện ra các dòng sản phẩm chi tiết
- **Nếu ấn tính số dư từng ngày:** Thêm 2 cột dư nợ/có

### Cách Tính Số Dư Từng Ngày

```
Công nợ dòng 1 = Đầu kỳ + PS Nợ - PS Có
Kết quả dòng 1 = Đầu kỳ cho dòng 2
Công nợ dòng 2 = Kết quả dòng 1 + PS Nợ dòng 2 - PS Có dòng 2
...và tiếp tục cho các dòng tiếp theo
```

**Ví dụ tính toán:**
```
Đầu kỳ: 1,000,000 VND (Nợ)

Dòng 1: Bán hàng +500,000 → Số dư: 1,500,000 (Nợ)
Dòng 2: Thu tiền -300,000 → Số dư: 1,200,000 (Nợ)
Dòng 3: Bán hàng +200,000 → Số dư: 1,400,000 (Nợ)
```

---


*Tài liệu này tập trung vào nghiệp vụ kế toán và sẽ được cập nhật thường xuyên khi có thêm thông tin.*
