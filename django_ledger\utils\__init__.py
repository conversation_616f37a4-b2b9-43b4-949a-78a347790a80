"""
Django Ledger utilities package.
"""

from django_ledger.utils.chung_tu_quyen_mapping import (
    ChungTuQuyenMapping,
    get_valid_quyens_for_chung_tu,
    is_valid_chung_tu_quyen_mapping,
    validate_chung_tu_quyen_mapping,
)

# Import session utilities from parent utils.py
try:
    import importlib.util
    import os

    # Get the path to the parent utils.py file
    parent_dir = os.path.dirname(os.path.dirname(__file__))
    utils_path = os.path.join(parent_dir, 'utils.py')

    # Load the module
    spec = importlib.util.spec_from_file_location("django_ledger_utils", utils_path)
    utils_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(utils_module)

    # Import the functions
    get_default_entity_session_key = utils_module.get_default_entity_session_key
    get_end_date_from_session = utils_module.get_end_date_from_session
    accruable_net_summary = utils_module.accruable_net_summary

except (ImportError, AttributeError, FileNotFoundError):
    # Fallback functions if not available
    def get_default_entity_session_key():
        return 'djl_default_entity_model'

    def get_end_date_from_session(entity_slug, request):
        from django_ledger.io.io_core import get_localdate
        return get_localdate()

    def accruable_net_summary(*args, **kwargs):
        return {}

# Add missing functions for io_generator
def generate_random_item_id():
    """Generate a random item ID."""
    import random
    import string
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

def generate_random_sku():
    """Generate a random SKU."""
    import random
    import string
    return 'SKU-' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

def generate_random_upc():
    """Generate a random UPC."""
    import random
    return ''.join([str(random.randint(0, 9)) for _ in range(12)])

__all__ = [
    'ChungTuQuyenMapping',
    'get_valid_quyens_for_chung_tu',
    'is_valid_chung_tu_quyen_mapping',
    'validate_chung_tu_quyen_mapping',
    'get_default_entity_session_key',
    'get_end_date_from_session',
    'accruable_net_summary',
    'generate_random_item_id',
    'generate_random_sku',
    'generate_random_upc',
]
