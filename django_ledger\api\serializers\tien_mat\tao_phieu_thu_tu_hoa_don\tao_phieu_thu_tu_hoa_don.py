"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for TaoPhieuThuTuHoaDonModel.
"""

from rest_framework import serializers
from django_ledger.api.serializers.account import (  # noqa: F401
    AccountModelSerializer,
)
from django_ledger.api.serializers.bank_account import BankAccountModelSerializer
from django_ledger.api.serializers.chung_tu import ChungTuSerializer
from django_ledger.api.serializers.quyen_chung_tu import QuyenChungTuListSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSimpleSerializer
from django_ledger.models.tien_mat.tao_phieu_thu_tu_hoa_don import TaoPhieuThuTuHoaDonModel


class TaoPhieuThuTuHoaDonSerializer(serializers.ModelSerializer):
    """
    Serializer for TaoPhieuThuTuHoaDonModel.
    """
    
    # Reference data fields
    unit_id_data = serializers.SerializerMethodField()
    ma_ct_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    tknh_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()

    class Meta:
        model = TaoPhieuThuTuHoaDonModel
        fields = [
            'uuid',
            'unit_id',
            'unit_id_data',
            'ma_ct',
            'ma_ct_data',
            'ngay_ct',
            'ma_nk',
            'ma_nk_data',
            'tknh',
            'tknh_data',
            'tk',
            'tk_data',
            'loai_pt',
            'ngay_hd_yn',
            'inv_code',
            'tblKeys',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'created', 'updated']

    def get_unit_id_data(self, obj):
        """Returns the unit data for the unit_id field."""
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_ma_ct_data(self, obj):
        """Returns the document type data for the ma_ct field."""
        if obj.ma_ct:
           return ChungTuSerializer(obj.ma_ct).data
        return None

    def get_ma_nk_data(self, obj):
        """Returns the operation data for the ma_nk field."""
        if obj.ma_nk:
            return QuyenChungTuListSerializer(obj.ma_nk).data
        return None

    def get_tknh_data(self, obj):
        """Returns the bank account data for the tknh field."""
        if obj.tknh:
            return BankAccountModelSerializer(obj.tknh).data
        return None

    def get_tk_data(self, obj):
        """Returns the account data for the tk field."""
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None


class TaoPhieuThuTuHoaDonFilterSerializer(serializers.Serializer):
    """
    Serializer for filtering unpaid invoices or created receipts.
    """
    xu_ly = serializers.ChoiceField(
        choices=[('1', 'Tạo phiếu thu'), ('2', 'Xóa phiếu thu')],
        required=False,
        default='1',
        help_text="Processing mode: '1' for creating receipts, '2' for deleting receipts"
    )
    ngay_ct1 = serializers.DateField(
        required=True,
        help_text="Start date for filtering (YYYY-MM-DD format)"
    )
    ngay_ct2 = serializers.DateField(
        required=True,
        help_text="End date for filtering (YYYY-MM-DD format)"
    )
    so_ct1 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Document number from"
    )
    so_ct2 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Document number to"
    )
    ma_ct = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Document type code (e.g., 'HD1', 'HD2') - FK to ChungTu"
    )
    ma_kh = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Customer code - FK to CustomerModel"
    )
    ma_nt = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Currency code - FK to NgoaiTeModel"
    )
    ma_nk = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Document book code - FK to QuyenChungTu"
    )
    unit_id = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text="Entity unit ID - FK to EntityUnitModel"
    )
    user_id0 = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="User ID for filtering"
    )

    def validate_ma_ct(self, value):
        """Validate document type code."""
        if value and value not in ['HD1', 'HD2']:
            raise serializers.ValidationError("Document type must be 'HD1' or 'HD2'")
        return value

    def validate_ma_kh(self, value):
        """Validate customer code - can be extended to check FK existence."""
        # For now, just basic validation. Can be extended to check CustomerModel existence
        return value

    def validate_ma_nt(self, value):
        """Validate currency code - can be extended to check FK existence."""
        # For now, just basic validation. Can be extended to check NgoaiTeModel existence
        return value

    def validate_ma_nk(self, value):
        """Validate document book code - can be extended to check FK existence."""
        # For now, just basic validation. Can be extended to check QuyenChungTu existence
        return value

    def validate_unit_id(self, value):
        """Validate entity unit ID - can be extended to check FK existence."""
        # For now, just basic validation. Can be extended to check EntityUnitModel existence
        return value

    def validate(self, data):
        """Validate filter data."""
        if data['ngay_ct1'] > data['ngay_ct2']:
            raise serializers.ValidationError("Start date must be before end date")
        return data


class TaoPhieuThuTuHoaDonResponseSerializer(serializers.Serializer):
    """
    Serializer for unpaid invoice response with exact fields as required.
    """
    id = serializers.CharField(help_text="ID để gom khi tạo phiếu thu")
    so_ct = serializers.CharField()
    ngay_ct = serializers.DateField()
    ma_ngv = serializers.CharField()
    ma_kh = serializers.CharField()
    tk = serializers.CharField()
    ma_nt = serializers.CharField()
    t_tt_nt = serializers.DecimalField(max_digits=18, decimal_places=4)
    status = serializers.CharField()
    ma_tthddt = serializers.CharField()
    unit_id = serializers.CharField()
    ma_ct = serializers.CharField()
    ma_unit = serializers.CharField()
    ten_kh = serializers.CharField()
    ten_ngv = serializers.CharField()
    ten_ttct = serializers.CharField()
    so_ct3 = serializers.CharField()
    ma_ct3 = serializers.CharField()
    id_ct3 = serializers.CharField()


class CreateReceiptFromInvoicesSerializer(serializers.Serializer):
    """
    Serializer for creating receipt from selected invoices.
    """
    selected_invoices = serializers.ListField(
        child=serializers.DictField(),
        help_text="List of selected invoices with ID"
    )
    receipt_data = serializers.DictField(
        help_text="Receipt data including ngay_ct, tk, unit_id, etc."
    )

    def validate_selected_invoices(self, value):
        """Validate selected invoices."""
        if not value:
            raise serializers.ValidationError("At least one invoice must be selected")

        for invoice in value:
            if 'id' not in invoice:
                raise serializers.ValidationError("Each invoice must have an ID")

        return value

    def validate_receipt_data(self, value):
        """Validate receipt data."""
        required_fields = ['ngay_ct', 'tk', 'unit_id']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"Receipt data must include {field}")

        return value
