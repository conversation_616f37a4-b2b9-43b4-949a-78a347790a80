#!/usr/bin/env python3
"""
Test script for TaoPhieuThuTuHoaDon filter with real data
Creates test data first, then tests the filter functionality
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ENTITY_SLUG = "tutimi-dnus2xnc"
DIRECT_TOKEN = "d37d77e4655f5aff352da29d8b1953338193d389"

class FilterRealDataTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Token {DIRECT_TOKEN}',
            'Content-Type': 'application/json'
        })
        self.base_url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp"
        self.filter_url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        self.created_data = {}
        
    def create_test_chung_tu(self):
        """Create test ChungTu data"""
        print("🔧 Creating test ChungTu data...")
        
        chung_tu_url = f"{self.base_url}/documents/"
        
        # Simple ChungTu data that should work
        chung_tu_data = [
            {
                "ma_ct": "HD1",
                "ten_ct": "Hóa đơn bán hàng",
                "ngay_ks": "2024-01-01",
                "stt": 1,
                "i_so_ct": 1000
            },
            {
                "ma_ct": "HD2", 
                "ten_ct": "Hóa đơn dịch vụ",
                "ngay_ks": "2024-01-01",
                "stt": 2,
                "i_so_ct": 1000
            }
        ]
        
        created_chung_tu = []
        
        for ct_data in chung_tu_data:
            print(f"Creating ChungTu: {ct_data['ma_ct']}")
            
            response = self.session.post(chung_tu_url, json=ct_data)
            
            if response.status_code == 201:
                created_data = response.json()
                created_chung_tu.append(created_data)
                print(f"✅ Created: {created_data.get('ma_ct')} (UUID: {created_data.get('uuid')})")
            elif response.status_code == 400:
                error_data = response.json()
                if 'ma_ct' in error_data and 'already exists' in str(error_data['ma_ct']):
                    print(f"⚠️ Already exists: {ct_data['ma_ct']}")
                    # Try to get existing record
                    list_response = self.session.get(chung_tu_url)
                    if list_response.status_code == 200:
                        existing_records = list_response.json().get('results', [])
                        for record in existing_records:
                            if record.get('ma_ct') == ct_data['ma_ct']:
                                created_chung_tu.append(record)
                                print(f"📋 Found existing: {record.get('ma_ct')} (UUID: {record.get('uuid')})")
                                break
                else:
                    print(f"❌ Failed to create {ct_data['ma_ct']}: {error_data}")
            else:
                print(f"❌ Failed to create {ct_data['ma_ct']}: {response.status_code}")
                print(f"Response: {response.text}")
        
        self.created_data['chung_tu'] = created_chung_tu
        return len(created_chung_tu) > 0
    
    def test_filter_with_real_data(self):
        """Test filter with real ChungTu data"""
        print("\n🧪 Testing filter with real data...")
        
        if not self.created_data.get('chung_tu'):
            print("❌ No ChungTu data available for testing")
            return False
        
        # Get HD1 ChungTu
        hd1_chung_tu = None
        for ct in self.created_data['chung_tu']:
            if ct.get('ma_ct') == 'HD1':
                hd1_chung_tu = ct
                break
        
        if not hd1_chung_tu:
            print("❌ No HD1 ChungTu found")
            return False
        
        print(f"Using ChungTu: {hd1_chung_tu.get('ma_ct')} (UUID: {hd1_chung_tu.get('uuid')})")
        
        # Calculate date range
        today = datetime.now()
        start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')
        
        # Test Case 1: Basic filter with real ChungTu UUID
        print("\n📝 Test Case 1: Basic filter with real ChungTu UUID")
        
        test_data = {
            "xu_ly": "1",  # Chế độ Tạo
            "ngay_ct1": start_date,
            "ngay_ct2": end_date,
            "ma_ct": hd1_chung_tu.get('uuid')
        }
        
        print(f"Request data: {json.dumps(test_data, indent=2)}")
        
        response = self.session.post(self.filter_url, json=test_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Filter successful")
            print(f"📊 Results count: {len(data.get('results', []))}")
            
            # Show sample result if available
            if data.get('results'):
                sample = data['results'][0]
                print(f"📋 Sample result fields: {list(sample.keys())}")
                print(f"📋 Sample data:")
                for key, value in list(sample.items())[:5]:  # Show first 5 fields
                    print(f"  - {key}: {value}")
            else:
                print("📋 No results found (expected if no invoice data exists)")
        else:
            print(f"❌ Filter failed")
            print(f"Response: {response.text}")
            return False
        
        # Test Case 2: Delete mode
        print("\n📝 Test Case 2: Delete mode filter")
        
        test_data_delete = {
            "xu_ly": "2",  # Chế độ Xóa
            "ngay_ct1": start_date,
            "ngay_ct2": end_date,
            "ma_ct": hd1_chung_tu.get('uuid')
        }
        
        print(f"Request data: {json.dumps(test_data_delete, indent=2)}")
        
        response = self.session.post(self.filter_url, json=test_data_delete)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Delete mode filter successful")
            print(f"📊 Results count: {len(data.get('results', []))}")
        else:
            print(f"❌ Delete mode filter failed")
            print(f"Response: {response.text}")
        
        return True
    
    def test_filter_with_customer_data(self):
        """Test filter with customer data if available"""
        print("\n🧪 Testing filter with customer data...")
        
        # Get customer data
        customer_url = f"{self.base_url}/customers/"
        response = self.session.get(customer_url)
        
        if response.status_code != 200:
            print("⚠️ Cannot get customer data, skipping customer filter test")
            return True
        
        customer_data = response.json()
        if not customer_data.get('results'):
            print("⚠️ No customer data found, skipping customer filter test")
            return True
        
        customer = customer_data['results'][0]
        print(f"Using customer: {customer.get('customer_name', 'Unknown')} (UUID: {customer.get('uuid')})")
        
        # Get ChungTu
        if not self.created_data.get('chung_tu'):
            print("❌ No ChungTu data available")
            return False
        
        hd1_chung_tu = None
        for ct in self.created_data['chung_tu']:
            if ct.get('ma_ct') == 'HD1':
                hd1_chung_tu = ct
                break
        
        if not hd1_chung_tu:
            print("❌ No HD1 ChungTu found")
            return False
        
        # Test with customer filter
        today = datetime.now()
        start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')
        
        test_data_with_customer = {
            "xu_ly": "1",
            "ngay_ct1": start_date,
            "ngay_ct2": end_date,
            "ma_ct": hd1_chung_tu.get('uuid'),
            "ma_kh": customer.get('uuid')
        }
        
        print(f"Request data: {json.dumps(test_data_with_customer, indent=2)}")
        
        response = self.session.post(self.filter_url, json=test_data_with_customer)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Filter with customer successful")
            print(f"📊 Results count: {len(data.get('results', []))}")
        else:
            print(f"❌ Filter with customer failed")
            print(f"Response: {response.text}")
        
        return True
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Filter Tests with Real Data")
        print("=" * 50)
        
        # Create test data
        if not self.create_test_chung_tu():
            print("❌ Failed to create test data")
            return False
        
        # Test filter with real data
        if not self.test_filter_with_real_data():
            return False
        
        # Test filter with customer data
        if not self.test_filter_with_customer_data():
            return False
        
        print("\n" + "=" * 50)
        print("✅ All real data tests completed!")
        return True

if __name__ == "__main__":
    tester = FilterRealDataTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Real data test suite passed!")
    else:
        print("\n💥 Real data test suite failed!")
        exit(1)
