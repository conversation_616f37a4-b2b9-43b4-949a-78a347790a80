"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service class for TaoPhieuThuTuHoaDonModel.
"""

from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from django.db.models import QuerySet
from django.db import transaction

from django_ledger.models.tien_mat.tao_phieu_thu_tu_hoa_don import TaoPhieuThuTuHoaDonModel
from django_ledger.repositories.tien_mat.tao_phieu_thu_tu_hoa_don import TaoPhieuThuTuHoaDonRepository
from django_ledger.services.base import BaseService
from django_ledger.utils.chung_tu_quyen_mapping import ChungTuQuyenMapping


class TaoPhieuThuTuHoaDonService(BaseService):
    """
    Service class for TaoPhieuThuTuHoaDonModel.
    Handles business logic for the model.
    """

    def __init__(self):
        """
        Initialize the service with the repository.
        """
        super().__init__()
        self.repository = TaoPhieuThuTuHoaDonRepository()

    def get_by_id(self, entity_slug: str, uuid: Union[str, UUID]) -> Optional[TaoPhieuThuTuHoaDonModel]:
        """
        Retrieves a TaoPhieuThuTuHoaDonModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the TaoPhieuThuTuHoaDonModel to retrieve.

        Returns
        -------
        Optional[TaoPhieuThuTuHoaDonModel]
            The TaoPhieuThuTuHoaDonModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:
        """
        Lists TaoPhieuThuTuHoaDonModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of TaoPhieuThuTuHoaDonModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def create(self, entity_slug: str, data: Dict[str, Any]) -> TaoPhieuThuTuHoaDonModel:
        """
        Creates a new TaoPhieuThuTuHoaDonModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new TaoPhieuThuTuHoaDonModel.

        Returns
        -------
        TaoPhieuThuTuHoaDonModel
            The created TaoPhieuThuTuHoaDonModel instance.
        """
        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)

        # Create the TaoPhieuThuTuHoaDonModel instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        return instance

    def update(self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[TaoPhieuThuTuHoaDonModel]:
        """
        Updates an existing TaoPhieuThuTuHoaDonModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the TaoPhieuThuTuHoaDonModel to update.
        data : Dict[str, Any]
            The data to update the TaoPhieuThuTuHoaDonModel with.

        Returns
        -------
        Optional[TaoPhieuThuTuHoaDonModel]
            The updated TaoPhieuThuTuHoaDonModel instance, or None if not found.
        """
        # Check existence before update
        existing_record = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not existing_record:
            raise ValueError(f"Record with UUID {uuid} not found")

        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)

        # Update the TaoPhieuThuTuHoaDonModel instance
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        return instance

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a TaoPhieuThuTuHoaDonModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the TaoPhieuThuTuHoaDonModel to delete.

        Returns
        -------
        bool
            True if the TaoPhieuThuTuHoaDonModel was deleted, False otherwise.
        """
        # Check existence before delete
        existing_record = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not existing_record:
            raise ValueError(f"Record with UUID {uuid} not found")

        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    def get_unpaid_invoices(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get unpaid invoices for creating receipts.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        filters : Dict[str, Any]
            Filter parameters for the query.

        Returns
        -------
        List[Dict[str, Any]]
            List of unpaid invoices.
        """
        # Validate ma_nk against ma_ct using simple utility
        ma_ct = filters.get('ma_ct')
        ma_nk = filters.get('ma_nk')

        if ma_ct and ma_nk:
            try:
                ChungTuQuyenMapping.validate_or_raise(ma_ct, ma_nk)
            except Exception as e:
                raise ValueError(f"Invalid filter combination: {str(e)}")

        return self.repository.get_unpaid_invoices(entity_slug=entity_slug, filters=filters)

    def get_valid_quyens_for_chung_tu(self, ma_ct: str) -> List[Dict[str, Any]]:
        """
        Get valid quyển chứng từ options for given chứng từ.

        Parameters
        ----------
        ma_ct : str
            Document type code.

        Returns
        -------
        List[Dict[str, Any]]
            List of valid quyển chứng từ options.
        """
        return ChungTuQuyenMapping.get_valid_quyens(ma_ct)

    def get_created_receipts(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get created receipts for deletion mode.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        filters : Dict[str, Any]
            Filter parameters for the query.

        Returns
        -------
        List[Dict[str, Any]]
            List of created receipts.
        """
        from django_ledger.services.tien_mat.hach_toan.phieu_thu.phieu_thu import PhieuThuService

        # Use PhieuThuService to get receipts
        phieu_thu_service = PhieuThuService()

        # Build filter parameters for PhieuThu
        phieu_thu_filters = {}

        # Date range filter
        if filters.get('ngay_ct_from'):
            phieu_thu_filters['ngay_ct__gte'] = filters['ngay_ct_from']
        if filters.get('ngay_ct_to'):
            phieu_thu_filters['ngay_ct__lte'] = filters['ngay_ct_to']

        # Document type filter (ma_ct)
        if filters.get('ma_ct'):
            phieu_thu_filters['ma_ct'] = filters['ma_ct']

        # Document book filter (ma_nk)
        if filters.get('ma_nk'):
            phieu_thu_filters['ma_nk'] = filters['ma_nk']

        # Customer filter
        if filters.get('ma_kh'):
            phieu_thu_filters['ma_kh'] = filters['ma_kh']

        # Get receipts from PhieuThuService
        receipts = phieu_thu_service.list(entity_slug=entity_slug, **phieu_thu_filters)

        # Convert to list format for API response
        result = []
        for receipt in receipts:
            result.append({
                'id': str(receipt.uuid),
                'so_ct': receipt.i_so_ct or '',
                'ngay_ct': receipt.ngay_ct.strftime('%d/%m/%Y') if receipt.ngay_ct else '',
                'loai_hoa_don': 'Phiếu thu',
                'ma_khach_hang': receipt.ma_kh.ma_kh if receipt.ma_kh else '',
                'ten_khach_hang': receipt.ma_kh.ten_kh if receipt.ma_kh else '',
                'tien_hang': float(receipt.t_tien or 0),
                'tien_thue': 0,  # PhieuThu doesn't have separate tax field
                'tong_tien': float(receipt.t_tien or 0),
                'ngay_tao': receipt.created.strftime('%d/%m/%Y') if receipt.created else '',
                'trang_thai': 'Đã tạo',
                'trang_thai_hqdt': 'Không sử dụng',
                'type': 'phieu_thu'
            })

        return result

    @transaction.atomic
    def delete_receipt_and_restore_invoices(self, entity_slug: str, phieu_thu_uuid: str) -> bool:
        """
        Delete a receipt and restore associated invoices.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        phieu_thu_uuid : str
            The UUID of the PhieuThu to delete.

        Returns
        -------
        bool
            True if the receipt was deleted successfully.
        """
        from django_ledger.services.tien_mat.hach_toan.phieu_thu.phieu_thu import PhieuThuService

        # First restore invoices
        self.restore_invoices_from_receipt(entity_slug=entity_slug, phieu_thu_uuid=phieu_thu_uuid)

        # Then delete the PhieuThu using PhieuThuService
        phieu_thu_service = PhieuThuService()
        return phieu_thu_service.delete(entity_slug=entity_slug, uuid=phieu_thu_uuid)

    @transaction.atomic
    def create_receipt_from_invoices(self, entity_slug: str, invoice_data: List[Dict[str, Any]], receipt_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a receipt from selected invoices and move them to PhieuThuModel.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        invoice_data : List[Dict[str, Any]]
            List of selected invoices with amounts.
        receipt_data : Dict[str, Any]
            Receipt data.

        Returns
        -------
        Dict[str, Any]
            Created receipt information.
        """
        from django_ledger.services.tien_mat.hach_toan.phieu_thu.phieu_thu import PhieuThuService
        
        # Create PhieuThu using PhieuThuService
        phieu_thu_service = PhieuThuService()
        
        # Prepare PhieuThu data
        phieu_thu_data = {
            'ngay_ct': receipt_data.get('ngay_ct'),
            'dien_giai': f"Phiếu thu từ {len(invoice_data)} hóa đơn",
            'tk': receipt_data.get('tk'),
            'unit_id': receipt_data.get('unit_id'),
            'ma_nk': receipt_data.get('ma_nk'),
            't_tien': sum(float(inv.get('amount', 0)) for inv in invoice_data),
            't_tien_nt': sum(float(inv.get('amount', 0)) for inv in invoice_data),
        }
        
        # Create PhieuThu
        phieu_thu = phieu_thu_service.create(entity_slug=entity_slug, data=phieu_thu_data)
        
        # Store invoice references in TaoPhieuThuTuHoaDon for tracking
        tracking_data = {
            'unit_id': receipt_data.get('unit_id'),
            'ma_ct': receipt_data.get('ma_ct'),
            'ngay_ct': receipt_data.get('ngay_ct'),
            'ma_nk': receipt_data.get('ma_nk'),
            'tknh': receipt_data.get('tknh'),
            'tk': receipt_data.get('tk'),
            'loai_pt': receipt_data.get('loai_pt', 'INVOICE'),
            'ngay_hd_yn': receipt_data.get('ngay_hd_yn', True),
            'inv_code': f"PT_{phieu_thu.uuid}",
            'tblKeys': [inv.get('id') for inv in invoice_data],
        }
        
        # Create tracking record
        tracking_record = self.create(entity_slug=entity_slug, data=tracking_data)
        
        # Update invoice statuses to PAID and remove from available invoices
        self._update_invoice_statuses(invoice_data, 'PAID')

        # Remove invoices from the available pool (they are now processed)
        self._remove_invoices_from_pool(entity_slug, invoice_data)
        
        return {
            'phieu_thu_uuid': str(phieu_thu.uuid),
            'tracking_uuid': str(tracking_record.uuid),
            'total_amount': phieu_thu_data['t_tien'],
            'invoice_count': len(invoice_data),
        }

    @transaction.atomic
    def restore_invoices_from_receipt(self, entity_slug: str, phieu_thu_uuid: str) -> bool:
        """
        Restore invoices when a receipt is deleted.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        phieu_thu_uuid : str
            The UUID of the deleted PhieuThu.

        Returns
        -------
        bool
            True if invoices were restored successfully.
        """
        # Find tracking record by inv_code
        tracking_records = self.list(entity_slug=entity_slug, inv_code=f"PT_{phieu_thu_uuid}")
        
        for tracking_record in tracking_records:
            if tracking_record.tblKeys:
                # Restore invoice statuses to UNPAID
                invoice_data = [{'id': invoice_id} for invoice_id in tracking_record.tblKeys]
                self._update_invoice_statuses(invoice_data, 'UNPAID')

                # Restore invoices back to the available pool
                self._restore_invoices_to_pool(entity_slug, tracking_record.tblKeys)

                # Delete tracking record
                self.delete(entity_slug=entity_slug, uuid=tracking_record.uuid)
        
        return True

    def _update_invoice_statuses(self, invoice_data: List[Dict[str, Any]], status: str):
        """
        Update invoice statuses.

        Parameters
        ----------
        invoice_data : List[Dict[str, Any]]
            List of invoices to update.
        status : str
            New status for the invoices.
        """
        from django.db import connection
        
        for invoice in invoice_data:
            invoice_id = invoice.get('id')
            invoice_type = invoice.get('type', 'ban_hang')

            if invoice_type == 'ban_hang':
                table_name = 'hoa_don_ban_hang'
            else:
                table_name = 'hoa_don_dich_vu'

            with connection.cursor() as cursor:
                cursor.execute(
                    f"UPDATE {table_name} SET status = %s WHERE id = %s",
                    [status, invoice_id]
                )

    def _remove_invoices_from_pool(self, entity_slug: str, invoice_data: List[Dict[str, Any]]):
        """
        Remove invoices from the available pool when they are processed.
        This simulates moving them out of the TaoPhieuThuTuHoaDon view.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        invoice_data : List[Dict[str, Any]]
            List of invoices to remove from pool.
        """
        # In this implementation, we just mark them as PAID
        # The filter query will exclude PAID invoices automatically
        pass

    def _restore_invoices_to_pool(self, entity_slug: str, invoice_uuids: List[str]):
        """
        Restore invoices back to the available pool when a receipt is deleted.
        This simulates moving them back to the TaoPhieuThuTuHoaDon view.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        invoice_uuids : List[str]
            List of invoice UUIDs to restore to pool.
        """
        # In this implementation, we just mark them as UNPAID
        # The filter query will include UNPAID invoices automatically
        pass
