#!/usr/bin/env python3
"""
Test script for TaoPhieuThuTuHoaDon filter functionality
Tests the updated serializer fields with proper UUID validation
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ENTITY_SLUG = "tutimi-dnus2xnc"
USERNAME = "tutimi"
PASSWORD = "1"
# Use direct token for testing
DIRECT_TOKEN = "d37d77e4655f5aff352da29d8b1953338193d389"

class TaoPhieuThuFilterTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.base_url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp"
        
    def authenticate(self):
        """Use direct token for testing"""
        print("🔐 Using direct token...")
        
        self.token = DIRECT_TOKEN
        self.session.headers.update({
            'Authorization': f'Token {self.token}',
            'Content-Type': 'application/json'
        })
        print(f"✅ Token set: {self.token[:20]}...")
        return True
    
    def get_reference_data(self):
        """Get reference data for testing (ChungTu, CustomerModel, etc.)"""
        print("📋 Getting reference data...")
        
        reference_data = {}
        
        # Get ChungTu data
        print("Getting ChungTu data...")
        chung_tu_url = f"{self.base_url}/documents/"
        response = self.session.get(chung_tu_url)
        if response.status_code == 200:
            chung_tu_data = response.json()
            if chung_tu_data.get('results'):
                reference_data['chung_tu'] = chung_tu_data['results']
                print(f"✅ Found {len(reference_data['chung_tu'])} ChungTu records")
                # Show available document types
                for ct in reference_data['chung_tu'][:3]:
                    print(f"  - {ct.get('ma_ct', 'N/A')}: {ct.get('ten_ct', 'N/A')} (UUID: {ct.get('uuid', 'N/A')})")
            else:
                print("⚠️ No ChungTu data found")
        else:
            print(f"❌ Failed to get ChungTu data: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Get Customer data
        print("Getting Customer data...")
        customer_url = f"{self.base_url}/customers/"
        response = self.session.get(customer_url)
        if response.status_code == 200:
            customer_data = response.json()
            if customer_data.get('results'):
                reference_data['customers'] = customer_data['results']
                print(f"✅ Found {len(reference_data['customers'])} Customer records")
                # Show first customer
                if reference_data['customers']:
                    customer = reference_data['customers'][0]
                    print(f"  - Sample: {customer.get('customer_name', 'N/A')} (UUID: {customer.get('uuid', 'N/A')})")
            else:
                print("⚠️ No Customer data found")
        else:
            print(f"❌ Failed to get Customer data: {response.status_code}")
        
        return reference_data
    
    def test_filter_with_uuid_fields(self, reference_data):
        """Test filter API with UUID fields"""
        print("\n🧪 Testing filter API with UUID fields...")
        
        filter_url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
        # Find HD1 or HD2 document type
        ma_ct_uuid = None
        if reference_data.get('chung_tu'):
            for ct in reference_data['chung_tu']:
                if ct.get('ma_ct') in ['HD1', 'HD2']:
                    ma_ct_uuid = ct.get('uuid')
                    print(f"Using ChungTu: {ct.get('ma_ct')} - {ct.get('ten_ct')} (UUID: {ma_ct_uuid})")
                    break
        
        if not ma_ct_uuid:
            print("❌ No suitable ChungTu (HD1/HD2) found for testing")
            return False
        
        # Calculate date range
        today = datetime.now()
        start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')
        
        # Test Case 1: Basic filter with required UUID fields
        print("\n📝 Test Case 1: Basic filter (Chế độ Tạo)")
        
        test_data_basic = {
            "xu_ly": "1",  # Chế độ Tạo
            "ngay_ct1": start_date,
            "ngay_ct2": end_date,
            "ma_ct": ma_ct_uuid  # UUID field
        }
        
        print(f"Request data: {json.dumps(test_data_basic, indent=2)}")
        
        response = self.session.post(filter_url, json=test_data_basic)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Basic filter successful")
            print(f"📊 Results count: {len(data.get('results', []))}")
            
            # Show sample result
            if data.get('results'):
                sample = data['results'][0]
                print(f"📋 Sample result fields: {list(sample.keys())}")
                print(f"📋 Sample data:")
                for key, value in list(sample.items())[:5]:  # Show first 5 fields
                    print(f"  - {key}: {value}")
        else:
            print(f"❌ Basic filter failed")
            print(f"Response: {response.text}")
            return False
        
        # Test Case 2: Filter with additional UUID fields
        print("\n📝 Test Case 2: Filter with additional UUID fields")
        
        test_data_extended = {
            "xu_ly": "1",
            "ngay_ct1": start_date,
            "ngay_ct2": end_date,
            "ma_ct": ma_ct_uuid
        }
        
        # Add customer UUID if available
        if reference_data.get('customers'):
            customer_uuid = reference_data['customers'][0].get('uuid')
            test_data_extended["ma_kh"] = customer_uuid
            print(f"Adding customer filter: {reference_data['customers'][0].get('customer_name', 'Unknown')} (UUID: {customer_uuid})")
        
        print(f"Request data: {json.dumps(test_data_extended, indent=2)}")
        
        response = self.session.post(filter_url, json=test_data_extended)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Extended filter successful")
            print(f"📊 Results count: {len(data.get('results', []))}")
        else:
            print(f"❌ Extended filter failed")
            print(f"Response: {response.text}")
        
        # Test Case 3: Delete mode (xu_ly = 2)
        print("\n📝 Test Case 3: Delete mode filter")
        
        test_data_delete = {
            "xu_ly": "2",  # Chế độ Xóa
            "ngay_ct1": start_date,
            "ngay_ct2": end_date,
            "ma_ct": ma_ct_uuid
        }
        
        print(f"Request data: {json.dumps(test_data_delete, indent=2)}")
        
        response = self.session.post(filter_url, json=test_data_delete)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Delete mode filter successful")
            print(f"📊 Results count: {len(data.get('results', []))}")
        else:
            print(f"❌ Delete mode filter failed")
            print(f"Response: {response.text}")
        
        return True
    
    def test_validation_errors(self):
        """Test validation errors with invalid data"""
        print("\n🚨 Testing validation errors...")
        
        filter_url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
        # Test Case 1: Missing required fields
        print("\n📝 Test Case 1: Missing required fields")
        
        invalid_data = {
            "xu_ly": "1"
            # Missing ngay_ct1, ngay_ct2, ma_ct
        }
        
        response = self.session.post(filter_url, json=invalid_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected validation error, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test Case 2: Invalid UUID format
        print("\n📝 Test Case 2: Invalid UUID format")
        
        invalid_uuid_data = {
            "xu_ly": "1",
            "ngay_ct1": "2024-01-01",
            "ngay_ct2": "2024-12-31",
            "ma_ct": "invalid-uuid-format"
        }
        
        response = self.session.post(filter_url, json=invalid_uuid_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ UUID validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected UUID validation error, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        # Test Case 3: Non-existent UUID
        print("\n📝 Test Case 3: Non-existent UUID")
        
        nonexistent_uuid_data = {
            "xu_ly": "1",
            "ngay_ct1": "2024-01-01",
            "ngay_ct2": "2024-12-31",
            "ma_ct": "12345678-1234-1234-1234-123456789012"  # Valid format but doesn't exist
        }
        
        response = self.session.post(filter_url, json=nonexistent_uuid_data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Non-existent UUID validation error correctly returned")
            errors = response.json()
            print(f"Errors: {json.dumps(errors, indent=2)}")
        else:
            print(f"❌ Expected validation error for non-existent UUID, got: {response.status_code}")
            print(f"Response: {response.text}")
        
        return True
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting TaoPhieuThuTuHoaDon Filter Tests (Updated Serializer)")
        print("=" * 60)
        
        # Authenticate
        if not self.authenticate():
            return False
        
        # Get reference data
        reference_data = self.get_reference_data()
        
        # Test filter API with UUID fields
        if not self.test_filter_with_uuid_fields(reference_data):
            return False
        
        # Test validation errors
        if not self.test_validation_errors():
            return False
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        return True

if __name__ == "__main__":
    tester = TaoPhieuThuFilterTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Test suite passed!")
    else:
        print("\n💥 Test suite failed!")
        exit(1)
