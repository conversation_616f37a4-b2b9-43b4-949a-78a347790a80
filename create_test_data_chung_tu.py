#!/usr/bin/env python3
"""
Create test data for ChungTu (Document Types) for testing
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ENTITY_SLUG = "tutimi-dnus2xnc"
DIRECT_TOKEN = "d37d77e4655f5aff352da29d8b1953338193d389"

def create_chung_tu_data():
    """Create ChungTu test data"""
    
    session = requests.Session()
    session.headers.update({
        'Authorization': f'Token {DIRECT_TOKEN}',
        'Content-Type': 'application/json'
    })
    
    base_url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp"
    chung_tu_url = f"{base_url}/documents/"
    
    # Test data for ChungTu
    chung_tu_data = [
        {
            "ma_ct": "HD1",
            "ten_ct": "<PERSON><PERSON><PERSON> đ<PERSON>n b<PERSON> hàng",
            "ten_ct2": "Sales Invoice",
            "ngay_ks": "2024-01-01",
            "stt": 1,
            "i_so_ct": 1000,
            "d_page_count": 0,
            "order_type": "0",
            "df_status": "5",
            "ct_kt_ton": "0",
            "loai_dl_ton": "0",
            "ct_sd_vi_tri": False,
            "ngay_lct_yn": True,
            "ct_save_log": "1",
            "xcode": "ban_hang",
            "user_id0_yn": True,
            "user_id2_yn": True
        },
        {
            "ma_ct": "HD2",
            "ten_ct": "Hóa đơn dịch vụ",
            "ten_ct2": "Service Invoice",
            "ngay_ks": "2024-01-01",
            "stt": 2,
            "i_so_ct": 1000,
            "d_page_count": 0,
            "order_type": "0",
            "df_status": "5",
            "ct_kt_ton": "0",
            "loai_dl_ton": "0",
            "ct_sd_vi_tri": False,
            "ngay_lct_yn": True,
            "ct_save_log": "1",
            "xcode": "ban_hang",
            "user_id0_yn": True,
            "user_id2_yn": True
        },
        {
            "ma_ct": "PT1",
            "ten_ct": "Phiếu thu tiền mặt",
            "ten_ct2": "Cash Receipt",
            "ngay_ks": "2024-01-01",
            "stt": 3,
            "i_so_ct": 1000,
            "d_page_count": 0,
            "order_type": "0",
            "df_status": "5",
            "ct_kt_ton": "0",
            "loai_dl_ton": "0",
            "ct_sd_vi_tri": False,
            "ngay_lct_yn": True,
            "ct_save_log": "1",
            "xcode": "tien_mat",
            "user_id0_yn": True,
            "user_id2_yn": True
        }
    ]
    
    print("🔧 Creating ChungTu test data...")
    
    created_records = []
    for ct_data in chung_tu_data:
        print(f"Creating ChungTu: {ct_data['ma_ct']} - {ct_data['ten_ct']}")
        
        response = session.post(chung_tu_url, json=ct_data)
        
        if response.status_code == 201:
            created_data = response.json()
            created_records.append(created_data)
            print(f"✅ Created: {created_data.get('ma_ct')} (UUID: {created_data.get('uuid')})")
        elif response.status_code == 400:
            error_data = response.json()
            if 'ma_ct' in error_data and 'already exists' in str(error_data['ma_ct']):
                print(f"⚠️ Already exists: {ct_data['ma_ct']}")
                # Try to get existing record
                list_response = session.get(chung_tu_url)
                if list_response.status_code == 200:
                    existing_records = list_response.json().get('results', [])
                    for record in existing_records:
                        if record.get('ma_ct') == ct_data['ma_ct']:
                            created_records.append(record)
                            print(f"📋 Found existing: {record.get('ma_ct')} (UUID: {record.get('uuid')})")
                            break
            else:
                print(f"❌ Failed to create {ct_data['ma_ct']}: {error_data}")
        else:
            print(f"❌ Failed to create {ct_data['ma_ct']}: {response.status_code}")
            print(f"Response: {response.text}")
    
    print(f"\n📊 Summary: {len(created_records)} ChungTu records available")
    return created_records

if __name__ == "__main__":
    create_chung_tu_data()
