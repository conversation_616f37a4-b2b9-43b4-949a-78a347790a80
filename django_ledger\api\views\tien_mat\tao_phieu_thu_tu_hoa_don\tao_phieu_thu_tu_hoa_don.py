"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for TaoPhieuThuTuHoaDonModel.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view

from django_ledger.api.serializers.tien_mat.tao_phieu_thu_tu_hoa_don import (
    TaoPhieuThuTuHoaDonSerializer,
    TaoPhieuThuTuHoaDonFilterSerializer,
    TaoPhieuThuTuHoaDonResponseSerializer,
    CreateReceiptFromInvoicesSerializer,
)
from django_ledger.api.views.common import ERPPagination
from django_ledger.services.tien_mat.tao_phieu_thu_tu_hoa_don import TaoPhieuThuTuHoaDonService
from django_ledger.utils.chung_tu_quyen_mapping import ChungTuQuy<PERSON>Mapping


@extend_schema_view(
    list=extend_schema(tags=["TaoPhieuThuTuHoaDon"]),
    create=extend_schema(tags=["TaoPhieuThuTuHoaDon"]),
    retrieve=extend_schema(tags=["TaoPhieuThuTuHoaDon"]),
    destroy=extend_schema(tags=["TaoPhieuThuTuHoaDon"]),
)
class TaoPhieuThuTuHoaDonViewSet(viewsets.ViewSet):
    """
    ViewSet for TaoPhieuThuTuHoaDonModel.
    Supports GET (filter), POST (create receipt), DELETE operations only.
    No PUT/PATCH operations as per requirements.
    """
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = TaoPhieuThuTuHoaDonService()

    def get_serializer_context(self):
        """
        Returns the serializer context.
        """
        return {
            'request': self.request,
            'entity_slug': self.kwargs.get('entity_slug')
        }

    def get_serializer(self, *args, **kwargs):
        """
        Returns the serializer instance.
        """
        kwargs['context'] = self.get_serializer_context()
        return TaoPhieuThuTuHoaDonSerializer(*args, **kwargs)

    @extend_schema(
        request=TaoPhieuThuTuHoaDonFilterSerializer,
        responses={200: TaoPhieuThuTuHoaDonResponseSerializer(many=True)},
        description="Filter and get unpaid invoices for creating receipts"
    )
    @action(detail=False, methods=['post'], url_path='filter')
    def filter_unpaid_invoices(self, request, entity_slug=None):
        """
        Filter and get unpaid invoices for creating receipts or created receipts for deletion.
        This is the main GET operation that requires filtering first.
        """
        # Validate filter data
        filter_serializer = TaoPhieuThuTuHoaDonFilterSerializer(data=request.data)
        if not filter_serializer.is_valid():
            return Response(filter_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Check processing mode from validated data
            xu_ly = filter_serializer.validated_data.get('xu_ly', '1')  # '1' or '2'

            if xu_ly == '2':
                # Get created receipts for deletion
                data = self.service.get_created_receipts(
                    entity_slug=entity_slug,
                    filters=filter_serializer.validated_data
                )
            else:
                # Get unpaid invoices for creation (default)
                data = self.service.get_unpaid_invoices(
                    entity_slug=entity_slug,
                    filters=filter_serializer.validated_data
                )

            # Serialize response
            response_serializer = TaoPhieuThuTuHoaDonResponseSerializer(data, many=True)

            # Apply pagination
            paginator = self.pagination_class()
            paginated_data = paginator.paginate_queryset(response_serializer.data, request)

            return paginator.get_paginated_response(paginated_data)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def list(self, request, entity_slug=None):
        """
        Lists TaoPhieuThuTuHoaDonModel instances for a specific entity.
        This returns tracking records, not the filtered invoices.

        Note: For getting unpaid invoices, use POST /filter/ endpoint instead.
        """
        return Response(
            {
                'message': 'To get unpaid invoices, use POST /filter/ endpoint with filter parameters.',
                'required_filters': ['ngay_ct1', 'ngay_ct2'],
                'optional_filters': ['so_ct1', 'so_ct2', 'ma_ct', 'ma_kh', 'ma_nt', 'ma_nk', 'unit_id'],
                'example': {
                    'ngay_ct1': '2025-01-01',
                    'ngay_ct2': '2025-12-31',
                    'ma_ct': 'HD1',
                    'ma_kh': '312323123'
                }
            },
            status=status.HTTP_200_OK
        )

    def retrieve(self, request, entity_slug=None, pk=None):
        """
        Retrieves a TaoPhieuThuTuHoaDonModel instance.
        """
        try:
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

            if not instance:
                return Response(
                    {'detail': 'TaoPhieuThuTuHoaDon not found.'},
                    status=status.HTTP_404_NOT_FOUND
                )

            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @extend_schema(
        request=CreateReceiptFromInvoicesSerializer,
        responses={201: dict},
        description="Create receipt from selected invoices"
    )
    def create(self, request, entity_slug=None):
        """
        Creates a receipt from selected invoices.
        This will move selected invoices to PhieuThuModel.
        """
        # Validate request data
        serializer = CreateReceiptFromInvoicesSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Extract data
            selected_invoices = serializer.validated_data['selected_invoices']
            receipt_data = serializer.validated_data['receipt_data']

            # Create receipt from invoices using service
            result = self.service.create_receipt_from_invoices(
                entity_slug=entity_slug,
                invoice_data=selected_invoices,
                receipt_data=receipt_data
            )

            return Response(result, status=status.HTTP_201_CREATED)

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, entity_slug=None, pk=None):
        """
        Deletes a TaoPhieuThuTuHoaDonModel instance.
        This will also restore the associated invoices.
        """
        try:
            # First restore invoices if this is a receipt deletion
            self.service.restore_invoices_from_receipt(entity_slug=entity_slug, phieu_thu_uuid=pk)
            
            # Then delete the tracking record
            success = self.service.delete(entity_slug=entity_slug, uuid=pk)

            if success:
                return Response(status=status.HTTP_204_NO_CONTENT)

            return Response(
                {'detail': 'TaoPhieuThuTuHoaDon not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @extend_schema(
        request=dict,
        responses={200: dict},
        description="Delete receipt and restore associated invoices"
    )
    @action(detail=False, methods=['post'], url_path='delete-receipt')
    def delete_receipt(self, request, entity_slug=None):
        """
        Delete a receipt and restore associated invoices.
        This is used in the "Xóa" mode.
        """
        phieu_thu_uuid = request.data.get('phieu_thu_uuid')
        if not phieu_thu_uuid:
            return Response(
                {'error': 'phieu_thu_uuid is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Delete receipt and restore invoices
            success = self.service.delete_receipt_and_restore_invoices(
                entity_slug=entity_slug,
                phieu_thu_uuid=phieu_thu_uuid
            )

            if success:
                return Response({
                    'success': True,
                    'message': 'Receipt deleted and invoices restored successfully'
                })
            else:
                return Response(
                    {'error': 'Failed to delete receipt'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @extend_schema(
        parameters=[
            {
                'name': 'ma_ct',
                'in': 'query',
                'required': True,
                'schema': {'type': 'string'},
                'description': 'Document type code (e.g., HD1, HD2)'
            }
        ],
        responses={200: dict},
        description="Get valid quyển chứng từ options for given chứng từ"
    )
    @action(detail=False, methods=['get'], url_path='quyen-options')
    def get_quyen_options(self, request, entity_slug=None):
        """
        Get available quyển chứng từ based on selected chứng từ.
        This implements the configurable approach instead of hard-coded if/else.
        """
        ma_ct = request.query_params.get('ma_ct')
        if not ma_ct:
            return Response(
                {'error': 'ma_ct parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Use simple utility to get valid options
            options = ChungTuQuyenMapping.get_valid_quyens(ma_ct)

            return Response({
                'ma_ct': ma_ct,
                'options': options,
                'count': len(options)
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
