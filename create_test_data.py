#!/usr/bin/env python3
"""
Create test data for TaoPhieuThuTuHoaDon testing
"""

import os
import sys
import django
from datetime import datetime, timedelta
from decimal import Decimal

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'x_erp.settings')
django.setup()

from django.contrib.auth.models import User
from django_ledger.models import (
    EntityModel,
    EntityUnitModel,
    CustomerModel,  # This is KhachHangModel
    ChungTu,        # This is ChungTuModel
    QuyenChungTu    # This is QuyenChungTuModel
)

def create_test_data():
    """Create test data for TaoPhieuThuTuHoaDon"""
    print("🚀 Creating test data for TaoPhieuThuTuHoaDon...")
    
    try:
        # Get existing entity
        entity_slug = "my-new-company-hydrqrbn"
        try:
            entity = EntityModel.objects.get(slug=entity_slug)
            print(f"✅ Entity: {entity.name} (exists)")
        except EntityModel.DoesNotExist:
            print(f"❌ Entity {entity_slug} not found. Please create it first.")
            return False
        
        # Get or create entity unit
        unit, created = EntityUnitModel.objects.get_or_create(
            entity_model=entity,
            defaults={
                'name': 'Main Unit',
                'slug': 'main-unit'
            }
        )
        print(f"✅ Unit: {unit.name} ({'created' if created else 'exists'})")
        
        # Create test customers
        customers_data = [
            {'ma_kh': 'KH001', 'ten_kh': 'Khách hàng 001', 'dia_chi': 'Hà Nội'},
            {'ma_kh': 'KH002', 'ten_kh': 'Khách hàng 002', 'dia_chi': 'TP.HCM'},
            {'ma_kh': 'KH003', 'ten_kh': 'Khách hàng 003', 'dia_chi': 'Đà Nẵng'},
        ]
        
        for customer_data in customers_data:
            customer, created = CustomerModel.objects.get_or_create(
                entity_model=entity,
                customer_number=customer_data['ma_kh'],
                defaults={
                    'customer_name': customer_data['ten_kh'],
                    'customer_address': customer_data['dia_chi'],
                    'is_active': True
                }
            )
            print(f"✅ Customer: {customer.customer_number} - {customer.customer_name} ({'created' if created else 'exists'})")
        
        # Create document types
        doc_types_data = [
            {'ma_ct': 'HD1', 'ten_ct': 'Hóa đơn bán hàng'},
            {'ma_ct': 'HD2', 'ten_ct': 'Hóa đơn dịch vụ'},
            {'ma_ct': 'PT', 'ten_ct': 'Phiếu thu'},
        ]
        
        for doc_data in doc_types_data:
            doc_type, created = ChungTu.objects.get_or_create(
                ma_ct=doc_data['ma_ct'],
                defaults={
                    'ten_ct': doc_data['ten_ct'],
                    'status': '1'
                }
            )
            print(f"✅ Document Type: {doc_type.ma_ct} - {doc_type.ten_ct} ({'created' if created else 'exists'})")
        
        # Create document books
        doc_books_data = [
            {'ma_nk': 'HDBH01', 'ten_nk': 'Quyển hóa đơn bán hàng 01', 'ma_ct': 'HD1'},
            {'ma_nk': 'HDDV01', 'ten_nk': 'Quyển hóa đơn dịch vụ 01', 'ma_ct': 'HD2'},
            {'ma_nk': 'PT01', 'ten_nk': 'Quyển phiếu thu 01', 'ma_ct': 'PT'},
        ]
        
        for book_data in doc_books_data:
            try:
                chung_tu = ChungTu.objects.get(ma_ct=book_data['ma_ct'])
                doc_book, created = QuyenChungTu.objects.get_or_create(
                    entity_model=entity,
                    ma_nk=book_data['ma_nk'],
                    defaults={
                        'ten_nk': book_data['ten_nk'],
                        'ma_ct': chung_tu,
                        'status': '1',
                        'so_bd': 1,
                        'so_kt': 999999
                    }
                )
                print(f"✅ Document Book: {doc_book.ma_nk} - {doc_book.ten_nk} ({'created' if created else 'exists'})")
            except ChungTu.DoesNotExist:
                print(f"❌ ChungTu {book_data['ma_ct']} not found")
        
        print("\n🎉 Test data creation completed!")
        print("\n📋 Summary:")
        print(f"  - Entity: {entity.slug}")
        print(f"  - Unit ID: {unit.id}")
        print(f"  - Customers: {len(customers_data)}")
        print(f"  - Document Types: {len(doc_types_data)}")
        print(f"  - Document Books: {len(doc_books_data)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_test_data()
    if success:
        print("\n✅ Test data ready for testing!")
    else:
        print("\n❌ Failed to create test data!")
