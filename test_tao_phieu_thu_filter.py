#!/usr/bin/env python3
"""
Test script for TaoPhieuThuTuHoaDon filter functionality
Tests both xu_ly modes: '1' (tạo) and '2' (xóa)
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ENTITY_SLUG = "my-new-company-hydrqrbn"
USERNAME = "admin"
PASSWORD = "1"

class TaoPhieuThuFilterTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.base_url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp"
        
    def authenticate(self):
        """Authenticate and get token"""
        print("🔐 Authenticating...")
        
        auth_url = f"{BASE_URL}/api/auth/token/"
        auth_data = {
            "username": USERNAME,
            "password": PASSWORD
        }
        
        response = self.session.post(auth_url, json=auth_data)
        print(f"Auth response status: {response.status_code}")
        print(f"Auth response text: {response.text}")

        if response.status_code == 200:
            try:
                response_data = response.json()
                self.token = response_data.get('token')
                if self.token:
                    self.session.headers.update({
                        'Authorization': f'Token {self.token}',
                        'Content-Type': 'application/json'
                    })
                    print(f"✅ Authentication successful. Token: {self.token[:20]}...")
                    return True
                else:
                    print(f"❌ No token in response: {response_data}")
                    return False
            except Exception as e:
                print(f"❌ Failed to parse JSON response: {e}")
                return False
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    def test_filter_mode_1_hd1(self):
        """Test xu_ly='1' with ma_ct='HD1' (hoa_don_ban_hang)"""
        print("\n📋 Testing Filter Mode 1 (Tạo) - HD1 (Hóa đơn bán hàng)")
        
        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
        # Calculate date range (last 30 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        
        filter_data = {
            "xu_ly": "1",  # Mode tạo
            "ma_ct": "HD1",  # Hóa đơn bán hàng
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "pageIndex": 1
        }
        
        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")
        
        response = self.session.post(url, json=filter_data)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} records")
            
            if data.get('results'):
                print("\n📄 Sample record:")
                sample = data['results'][0]
                print(f"  - ID: {sample.get('id')}")
                print(f"  - Số CT: {sample.get('so_ct')}")
                print(f"  - Ngày CT: {sample.get('ngay_ct')}")
                print(f"  - Khách hàng: {sample.get('ten_kh')}")
                print(f"  - Tổng tiền: {sample.get('t_tt_nt')}")
                print(f"  - Trạng thái: {sample.get('status')}")
            else:
                print("📝 No records found (empty result set)")
                
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")
            
        return response.status_code == 200
    
    def test_filter_mode_1_hd2(self):
        """Test xu_ly='1' with ma_ct='HD2' (hoa_don_dich_vu)"""
        print("\n📋 Testing Filter Mode 1 (Tạo) - HD2 (Hóa đơn dịch vụ)")
        
        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
        # Calculate date range (last 30 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        
        filter_data = {
            "xu_ly": "1",  # Mode tạo
            "ma_ct": "HD2",  # Hóa đơn dịch vụ
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "pageIndex": 1
        }
        
        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")
        
        response = self.session.post(url, json=filter_data)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} records")
            
            if data.get('results'):
                print("\n📄 Sample record:")
                sample = data['results'][0]
                print(f"  - ID: {sample.get('id')}")
                print(f"  - Số CT: {sample.get('so_ct')}")
                print(f"  - Ngày CT: {sample.get('ngay_ct')}")
                print(f"  - Khách hàng: {sample.get('ten_kh')}")
                print(f"  - Tổng tiền: {sample.get('t_tt_nt')}")
                print(f"  - Trạng thái: {sample.get('status')}")
            else:
                print("📝 No records found (empty result set)")
                
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")
            
        return response.status_code == 200
    
    def test_filter_mode_2(self):
        """Test xu_ly='2' (xóa) - should query phieu_thu"""
        print("\n📋 Testing Filter Mode 2 (Xóa) - Phiếu thu")
        
        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
        # Calculate date range (last 30 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        
        filter_data = {
            "xu_ly": "2",  # Mode xóa
            "ma_ct": "HD1",  # Still need ma_ct for filtering
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "pageIndex": 1
        }
        
        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")
        
        response = self.session.post(url, json=filter_data)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} receipts")
            
            if data.get('results'):
                print("\n📄 Sample receipt:")
                sample = data['results'][0]
                print(f"  - ID: {sample.get('id')}")
                print(f"  - Số CT: {sample.get('so_ct')}")
                print(f"  - Ngày CT: {sample.get('ngay_ct')}")
                print(f"  - Loại: {sample.get('loai_hoa_don')}")
                print(f"  - Khách hàng: {sample.get('ten_khach_hang')}")
                print(f"  - Tổng tiền: {sample.get('tong_tien')}")
                print(f"  - Trạng thái: {sample.get('trang_thai')}")
            else:
                print("📝 No receipts found (empty result set)")
                
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")
            
        return response.status_code == 200
    
    def test_additional_filters(self):
        """Test additional filter conditions"""
        print("\n📋 Testing Additional Filters")
        
        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"
        
        # Calculate date range (last 7 days for more specific test)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=7)
        
        filter_data = {
            "xu_ly": "1",
            "ma_ct": "HD1",
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "so_ct1": "1",  # Document number range
            "so_ct2": "999999",
            "pageIndex": 1
        }
        
        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")
        
        response = self.session.post(url, json=filter_data)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} records with additional filters")
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")
            
        return response.status_code == 200
    
    def run_all_tests(self):
        """Run all filter tests"""
        print("🚀 Starting TaoPhieuThuTuHoaDon Filter Tests")
        print("=" * 60)
        
        if not self.authenticate():
            return False
        
        results = []
        
        # Test Mode 1 - HD1
        results.append(self.test_filter_mode_1_hd1())
        
        # Test Mode 1 - HD2  
        results.append(self.test_filter_mode_1_hd2())
        
        # Test Mode 2
        results.append(self.test_filter_mode_2())
        
        # Test Additional Filters
        results.append(self.test_additional_filters())
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print(f"✅ Passed: {sum(results)}")
        print(f"❌ Failed: {len(results) - sum(results)}")
        print(f"📈 Success Rate: {sum(results)/len(results)*100:.1f}%")
        
        return all(results)

if __name__ == "__main__":
    tester = TaoPhieuThuFilterTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")
