#!/usr/bin/env python3
"""
Test script for TaoPhieuThuTuHoaDon filter functionality
Tests both xu_ly modes: '1' (tạo) and '2' (xóa)
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://127.0.0.1:8000"
ENTITY_SLUG = "my-new-company-hydrqrbn"
USERNAME = "tutimi"
PASSWORD = "1"
# Use direct token for testing
DIRECT_TOKEN = "d37d77e4655f5aff352da29d8b1953338193d389"

class TaoPhieuThuFilterTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.base_url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp"
        
    def authenticate(self):
        """Use direct token for testing"""
        print("🔐 Using direct token...")

        self.token = DIRECT_TOKEN
        self.session.headers.update({
            'Authorization': f'Token {self.token}',
            'Content-Type': 'application/json'
        })
        print(f"✅ Token set: {self.token[:20]}...")
        return True
    
    def test_filter_mode_1_hd1_basic(self):
        """Test xu_ly='1' with ma_ct='HD1' - Basic filter"""
        print("\n📋 Testing Filter Mode 1 (Tạo) - HD1 Basic")

        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"

        # Calculate date range (last 30 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)

        filter_data = {
            "xu_ly": "1",  # Mode tạo
            "ma_ct": "HD1",  # Hóa đơn bán hàng
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d")
        }

        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")

        response = self.session.post(url, json=filter_data)

        print(f"Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} records")

            if data.get('results'):
                print("\n📄 Sample record:")
                sample = data['results'][0]
                print(f"  - ID: {sample.get('id')}")
                print(f"  - Số CT: {sample.get('so_ct')}")
                print(f"  - Ngày CT: {sample.get('ngay_ct')}")
                print(f"  - Khách hàng: {sample.get('ten_kh')}")
                print(f"  - Tổng tiền: {sample.get('t_tt_nt')}")
                print(f"  - Trạng thái: {sample.get('status')}")
            else:
                print("📝 No records found (empty result set)")

        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")

        return response.status_code == 200
    
    def test_filter_mode_1_hd1_advanced(self):
        """Test xu_ly='1' with ma_ct='HD1' - Advanced filters with string values"""
        print("\n📋 Testing Filter Mode 1 (Tạo) - HD1 Advanced Filters (String Values)")

        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"

        # Calculate date range (last 30 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)

        filter_data = {
            "xu_ly": "1",  # Mode tạo
            "ma_ct": "HD1",  # Hóa đơn bán hàng (string for backward compatibility)
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "so_ct1": "1",  # Số chứng từ từ
            "so_ct2": "999999",  # Số chứng từ đến
            "ma_kh": "",  # Mã khách hàng (để trống để test tất cả)
            "ma_nt": "",  # Mã ngoại tệ (để trống)
            "ma_nk": "",  # Mã quyển chứng từ (để trống)
            "user_id0": ""  # User ID (để trống)
        }

        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")

        response = self.session.post(url, json=filter_data)

        print(f"Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} records")

            if data.get('results'):
                print("\n📄 Sample record:")
                sample = data['results'][0]
                print(f"  - ID: {sample.get('id')}")
                print(f"  - Số CT: {sample.get('so_ct')}")
                print(f"  - Ngày CT: {sample.get('ngay_ct')}")
                print(f"  - Khách hàng: {sample.get('ten_kh')}")
                print(f"  - Mã KH: {sample.get('ma_kh')}")
                print(f"  - Tổng tiền: {sample.get('t_tt_nt')}")
                print(f"  - Trạng thái: {sample.get('status')}")
                print(f"  - Unit ID: {sample.get('unit_id')}")
            else:
                print("📝 No records found (empty result set)")

        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")

        return response.status_code == 200
    
    def test_filter_mode_1_hd2(self):
        """Test xu_ly='1' with ma_ct='HD2' (hoa_don_dich_vu)"""
        print("\n📋 Testing Filter Mode 1 (Tạo) - HD2 (Hóa đơn dịch vụ)")

        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"

        # Calculate date range (last 30 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)

        filter_data = {
            "xu_ly": "1",  # Mode tạo
            "ma_ct": "HD2",  # Hóa đơn dịch vụ
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "ma_kh": "",  # Mã khách hàng (để trống)
            "ma_nt": ""  # Mã ngoại tệ (để trống)
        }

        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")

        response = self.session.post(url, json=filter_data)

        print(f"Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} records")

            if data.get('results'):
                print("\n📄 Sample record:")
                sample = data['results'][0]
                print(f"  - ID: {sample.get('id')}")
                print(f"  - Số CT: {sample.get('so_ct')}")
                print(f"  - Ngày CT: {sample.get('ngay_ct')}")
                print(f"  - Khách hàng: {sample.get('ten_kh')}")
                print(f"  - Tổng tiền: {sample.get('t_tt_nt')}")
                print(f"  - Trạng thái: {sample.get('status')}")
            else:
                print("📝 No records found (empty result set)")

        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")

        return response.status_code == 200
    
    def test_filter_mode_2(self):
        """Test xu_ly='2' (xóa) - should query phieu_thu"""
        print("\n📋 Testing Filter Mode 2 (Xóa) - Phiếu thu")

        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"

        # Calculate date range (last 30 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)

        filter_data = {
            "xu_ly": "2",  # Mode xóa
            "ma_ct": "HD1",  # Still need ma_ct for filtering
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "ma_kh": "",  # Mã khách hàng
            "ma_nk": "",  # Mã quyển chứng từ
            "unit_id": 1  # ID đơn vị
        }

        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")

        response = self.session.post(url, json=filter_data)

        print(f"Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} receipts")

            if data.get('results'):
                print("\n📄 Sample receipt:")
                sample = data['results'][0]
                print(f"  - ID: {sample.get('id')}")
                print(f"  - Số CT: {sample.get('so_ct')}")
                print(f"  - Ngày CT: {sample.get('ngay_ct')}")
                print(f"  - Loại: {sample.get('loai_hoa_don')}")
                print(f"  - Khách hàng: {sample.get('ten_khach_hang')}")
                print(f"  - Tổng tiền: {sample.get('tong_tien')}")
                print(f"  - Trạng thái: {sample.get('trang_thai')}")
            else:
                print("📝 No receipts found (empty result set)")

        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")

        return response.status_code == 200

    def test_filter_with_specific_customer(self):
        """Test filter with specific customer code"""
        print("\n📋 Testing Filter with Specific Customer")

        url = f"{self.base_url}/tien-mat/tao-phieu-thu-tu-hoa-don/filter/"

        # Calculate date range (last 60 days for broader search)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=60)

        filter_data = {
            "xu_ly": "1",
            "ma_ct": "HD1",
            "ngay_ct1": start_date.strftime("%Y-%m-%d"),
            "ngay_ct2": end_date.strftime("%Y-%m-%d"),
            "ma_kh": "KH001",  # Specific customer code
            "ma_nt": "VND",
            "so_ct1": "1",
            "so_ct2": "999999",
            "unit_id": 1
        }

        print(f"Request URL: {url}")
        print(f"Request Data: {json.dumps(filter_data, indent=2)}")

        response = self.session.post(url, json=filter_data)

        print(f"Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {data.get('count', 0)} records for customer KH001")
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")

        return response.status_code == 200
    
    def run_all_tests(self):
        """Run all filter tests"""
        print("🚀 Starting TaoPhieuThuTuHoaDon Filter Tests")
        print("=" * 60)
        
        if not self.authenticate():
            return False
        
        results = []

        # Test Mode 1 - HD1 Basic
        results.append(self.test_filter_mode_1_hd1_basic())

        # Test Mode 1 - HD1 Advanced
        results.append(self.test_filter_mode_1_hd1_advanced())

        # Test Mode 1 - HD2
        results.append(self.test_filter_mode_1_hd2())

        # Test Mode 2
        results.append(self.test_filter_mode_2())

        # Test with Specific Customer
        results.append(self.test_filter_with_specific_customer())
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print(f"✅ Passed: {sum(results)}")
        print(f"❌ Failed: {len(results) - sum(results)}")
        print(f"📈 Success Rate: {sum(results)/len(results)*100:.1f}%")
        
        return all(results)

if __name__ == "__main__":
    tester = TaoPhieuThuFilterTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed. Check the output above.")
