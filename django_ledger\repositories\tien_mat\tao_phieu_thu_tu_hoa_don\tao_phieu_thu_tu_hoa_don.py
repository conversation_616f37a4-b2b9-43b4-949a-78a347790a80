"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository class for TaoPhieuThuTuHoaDonModel.
"""

from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from django.db.models import QuerySet

from django_ledger.models.tien_mat.tao_phieu_thu_tu_hoa_don import TaoPhieuThuTuHoaDonModel
from django_ledger.repositories.base import BaseRepository


class TaoPhieuThuTuHoaDonRepository(BaseRepository):
    """
    Repository class for TaoPhieuThuTuHoaDonModel.
    Handles database operations for the model.
    """

    def __init__(self):
        """
        Initialize the repository with the TaoPhieuThuTuHoaDonModel.
        """
        super().__init__(model_class=TaoPhieuThuTuHoaDonModel)

    def get_queryset(self) -> QuerySet:
        """
        Returns the base queryset for TaoPhieuThuTuHoaDonModel.

        Returns
        -------
        QuerySet
            The base queryset for TaoPhieuThuTuHoaDonModel.
        """
        return self.model_class.objects.all().select_related(
            'unit_id',
            'ma_ct',
            'ma_nk',
            'tknh',
            'tk',
        )

    def get_by_id(self, entity_slug: str, uuid: Union[str, UUID]) -> Optional[TaoPhieuThuTuHoaDonModel]:
        """
        Retrieves a TaoPhieuThuTuHoaDonModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the TaoPhieuThuTuHoaDonModel to retrieve.

        Returns
        -------
        Optional[TaoPhieuThuTuHoaDonModel]
            The TaoPhieuThuTuHoaDonModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.for_entity(entity_slug=entity_slug).get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:
        """
        Lists TaoPhieuThuTuHoaDonModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of TaoPhieuThuTuHoaDonModel instances.
        """
        return self.model_class.objects.for_entity(entity_slug=entity_slug).filter(**kwargs)

    def create(self, entity_slug: str, data: Dict[str, Any]) -> TaoPhieuThuTuHoaDonModel:
        """
        Creates a new TaoPhieuThuTuHoaDonModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug (used for consistency with BaseRepository interface).
        data : Dict[str, Any]
            The data for the new TaoPhieuThuTuHoaDonModel.

        Returns
        -------
        TaoPhieuThuTuHoaDonModel
            The created TaoPhieuThuTuHoaDonModel instance.
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)

        # Create the TaoPhieuThuTuHoaDonModel instance
        instance = self.model_class(**data)
        instance.save()

        return instance

    def update(self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[TaoPhieuThuTuHoaDonModel]:
        """
        Updates an existing TaoPhieuThuTuHoaDonModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the TaoPhieuThuTuHoaDonModel to update.
        data : Dict[str, Any]
            The data to update the TaoPhieuThuTuHoaDonModel with.

        Returns
        -------
        Optional[TaoPhieuThuTuHoaDonModel]
            The updated TaoPhieuThuTuHoaDonModel instance, or None if not found.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)
            
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a TaoPhieuThuTuHoaDonModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the TaoPhieuThuTuHoaDonModel to delete.

        Returns
        -------
        bool
            True if the TaoPhieuThuTuHoaDonModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def get_unpaid_invoices(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get unpaid invoices from both HoaDonBanHang and HoaDonDichVu models.
        Implements proper filtering according to the request format.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        filters : Dict[str, Any]
            Filter parameters for the query.

        Returns
        -------
        List[Dict[str, Any]]
            List of unpaid invoices.
        """
        from django.db import connection
        from django_ledger.models import EntityModel

        try:
            entity = EntityModel.objects.get(slug=entity_slug)
            entity_uuid = str(entity.uuid)
        except EntityModel.DoesNotExist:
            return []

        # Build WHERE conditions based on filters
        where_conditions = []
        params = []

        # Entity filter
        where_conditions.append("hd.entity_model_id = %s")
        params.append(entity_uuid)

        # Date range filter (required)
        if filters.get('ngay_ct1') and filters.get('ngay_ct2'):
            where_conditions.append("hd.ngay_ct BETWEEN %s AND %s")
            params.extend([filters['ngay_ct1'], filters['ngay_ct2']])

        # Document number range filters
        if filters.get('so_ct1'):
            where_conditions.append("hd.so_ct >= %s")
            params.append(filters['so_ct1'])

        if filters.get('so_ct2'):
            where_conditions.append("hd.so_ct <= %s")
            params.append(filters['so_ct2'])

        # Document type filter - lookup by UUID to get ma_ct
        if filters.get('ma_ct'):
            from django_ledger.models import ChungTu
            # Use only() to select only needed fields to avoid missing column issues
            chung_tu = ChungTu.objects.filter(uuid=filters['ma_ct']).only('ma_ct').first()
            if chung_tu:
                where_conditions.append("ct.ma_ct = %s")
                params.append(chung_tu.ma_ct)
            else:
                # If UUID not found, no results should be returned
                where_conditions.append("1 = 0")

        # Customer filter - lookup by UUID
        if filters.get('ma_kh'):
            where_conditions.append("kh.uuid = %s")
            params.append(str(filters['ma_kh']))

        # Currency filter - lookup by UUID
        if filters.get('ma_nt'):
            where_conditions.append("nt.uuid = %s")
            params.append(str(filters['ma_nt']))

        # Document book filter - lookup by UUID
        if filters.get('ma_nk'):
            where_conditions.append("nk.uuid = %s")
            params.append(str(filters['ma_nk']))

        # Unit filter - lookup by UUID
        if filters.get('unit_id'):
            where_conditions.append("hd.unit_id_id = %s")
            params.append(str(filters['unit_id']))

        # Status filter - only unpaid invoices
        where_conditions.append("hd.status NOT IN ('PAID', 'CANCELLED')")

        where_clause = " AND ".join(where_conditions)

        # Determine which table to query based on ma_ct UUID (always provided by frontend)
        ma_ct_uuid = filters.get('ma_ct')

        # Lookup ChungTu to get ma_ct code
        if ma_ct_uuid:
            from django_ledger.models import ChungTu
            # Use only() to select only needed fields to avoid missing column issues
            chung_tu = ChungTu.objects.filter(uuid=ma_ct_uuid).only('ma_ct').first()
            if chung_tu:
                ma_ct_code = chung_tu.ma_ct
            else:
                raise ValueError(f"ChungTu with UUID {ma_ct_uuid} does not exist")
        else:
            raise ValueError("ma_ct is required for filtering")

        if ma_ct_code == 'HD1':
            # Query only hoa_don_ban_hang table
            table_name = 'hoa_don_ban_hang'
            sql_query = f"""
                SELECT
                    COALESCE(hd.id, 0) as id,
                    hd.so_ct,
                    hd.ngay_ct,
                    COALESCE(hd.ma_ngv, '') as ma_ngv,
                    COALESCE(kh.ma_kh, '') as ma_kh,
                    COALESCE(tk.code, '') as tk,
                    COALESCE(nt.ma_nt, '') as ma_nt,
                    COALESCE(hd.t_tt_nt, 0) as t_tt_nt,
                    COALESCE(hd.status, '') as status,
                    COALESCE(hd.ma_tthddt, '') as ma_tthddt,
                    COALESCE(hd.unit_id_id::text, '') as unit_id,
                    COALESCE(ct.ma_ct, '') as ma_ct,
                    COALESCE(eu.entity_id, '') as ma_unit,
                    COALESCE(kh.ten_kh, '') as ten_kh,
                    COALESCE(nv.ten_nv, '') as ten_ngv,
                    COALESCE(ct.ten_ct, '') as ten_ttct,
                    hd.so_ct as so_ct3,
                    COALESCE(ct.ma_ct, '') as ma_ct3,
                    COALESCE(hd.uuid::text, '') as id_ct3
                FROM {table_name} hd
                LEFT JOIN django_ledger_customermodel kh ON hd.ma_kh_id = kh.uuid
                LEFT JOIN django_ledger_accountmodel tk ON hd.tk_id = tk.uuid
                LEFT JOIN django_ledger_currencymodel nt ON hd.ma_nt_id = nt.uuid
                LEFT JOIN django_ledger_chungtu ct ON hd.ma_ct_id = ct.uuid
                LEFT JOIN django_ledger_entityunitmodel eu ON hd.unit_id_id = eu.uuid
                LEFT JOIN django_ledger_quyenchungtu nk ON hd.ma_nk_id = nk.uuid
                LEFT JOIN django_ledger_employeemodel nv ON hd.ma_ngv_id = nv.uuid
                WHERE {where_clause}
                ORDER BY hd.ngay_ct DESC, hd.so_ct
            """
        elif ma_ct_code == 'HD2':
            # Query only hoa_don_dich_vu table
            table_name = 'hoa_don_dich_vu'
            sql_query = f"""
                SELECT
                    COALESCE(hd.id, 0) as id,
                    COALESCE(so.so_ct, '') as so_ct,
                    hd.ngay_ct,
                    COALESCE(hd.ma_ngv, '') as ma_ngv,
                    COALESCE(kh.ma_kh, '') as ma_kh,
                    COALESCE(tk.code, '') as tk,
                    COALESCE(nt.ma_nt, '') as ma_nt,
                    COALESCE(hd.t_tt_nt, 0) as t_tt_nt,
                    COALESCE(hd.status, '') as status,
                    COALESCE(hd.ma_tthddt, '') as ma_tthddt,
                    COALESCE(hd.unit_id_id::text, '') as unit_id,
                    COALESCE(ct.ma_ct, '') as ma_ct,
                    COALESCE(eu.entity_id, '') as ma_unit,
                    COALESCE(kh.ten_kh, '') as ten_kh,
                    COALESCE(nv.ten_nv, '') as ten_ngv,
                    COALESCE(ct.ten_ct, '') as ten_ttct,
                    COALESCE(so.so_ct, '') as so_ct3,
                    COALESCE(ct.ma_ct, '') as ma_ct3,
                    COALESCE(hd.uuid::text, '') as id_ct3
                FROM {table_name} hd
                LEFT JOIN django_ledger_customermodel kh ON hd.ma_kh_id = kh.uuid
                LEFT JOIN django_ledger_accountmodel tk ON hd.tk_id = tk.uuid
                LEFT JOIN django_ledger_currencymodel nt ON hd.ma_nt_id = nt.uuid
                LEFT JOIN django_ledger_chungtu ct ON hd.ma_ct_id = ct.uuid
                LEFT JOIN django_ledger_entityunitmodel eu ON hd.unit_id_id = eu.uuid
                LEFT JOIN django_ledger_quyenchungtu nk ON hd.ma_nk_id = nk.uuid
                LEFT JOIN django_ledger_sochungtu so ON hd.so_ct_id = so.uuid
                LEFT JOIN django_ledger_employeemodel nv ON hd.ma_ngv_id = nv.uuid
                WHERE {where_clause.replace('hd.so_ct', 'so.so_ct')}
                ORDER BY hd.ngay_ct DESC, so.so_ct
            """
        else:
            # Invalid ma_ct - should not happen with proper frontend
            raise ValueError(f"Invalid ma_ct: {ma_ct_code}. Expected 'HD1' or 'HD2'")

        with connection.cursor() as cursor:
            # Execute single table query
            cursor.execute(sql_query, params)
            columns = [col[0] for col in cursor.description]
            results = []

            for row in cursor.fetchall():
                row_dict = dict(zip(columns, row))
                results.append(row_dict)

            return results
