#!/usr/bin/env python3
"""
Simple authentication test
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000"
USERNAME = "tutimi"
PASSWORD = "1"

def test_auth():
    """Test authentication"""
    print("🔐 Testing Authentication...")
    
    auth_url = f"{BASE_URL}/api/auth/token/"
    auth_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    print(f"URL: {auth_url}")
    print(f"Data: {json.dumps(auth_data, indent=2)}")
    
    try:
        response = requests.post(auth_url, json=auth_data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"✅ Success! Token: {token}")
            return token
        else:
            print(f"❌ Failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

if __name__ == "__main__":
    test_auth()
